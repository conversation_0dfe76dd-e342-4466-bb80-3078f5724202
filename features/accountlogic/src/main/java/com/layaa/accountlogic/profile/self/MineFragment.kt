package com.layaa.accountlogic.profile.self

import android.annotation.SuppressLint
import android.content.Intent
import android.os.Bundle
import android.text.TextUtils
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.view.isVisible
import com.alibaba.android.arouter.facade.annotation.Route
import com.layaa.accountapi.bean.BannedData
import com.layaa.accountapi.event.UpdateBeanEvent
import com.layaa.accountapi.event.UpdateDiamondEvent
import com.layaa.accountapi.event.UpdateGemEvent
import com.layaa.accountapi.event.UpdateGoldEvent
import com.layaa.accountapi.event.UpdateHeadEvent
import com.layaa.accountapi.event.UpdateNicknameEvent
import com.layaa.accountapi.friend.FriendConst
import com.layaa.accountapi.friend.FriendRouter.gotoFriendList
import com.layaa.accountapi.login.LoginPath
import com.layaa.accountapi.login.LoginRouter
import com.layaa.accountapi.profile.ProfileRouter
import com.layaa.accountapi.wallet.WalletConst
import com.layaa.accountapi.wallet.WalletRouter
import com.layaa.accountapi.wallet.WalletRouter.gotoWallet
import com.layaa.accountlogic.R
import com.layaa.accountlogic.account.UserManager
import com.layaa.accountlogic.bean.UserProfileEntity
import com.layaa.accountlogic.databinding.AccountFragmentMineBinding
import com.layaa.accountlogic.setting.AboutUsActivity
import com.layaa.accountlogic.setting.SettingActivity
import com.layaa.familyapi.FamilyRouter
import com.layaa.language.LanguageNotification
import com.layaa.language.LanguageObserve
import com.layaa.levelinfoapi.LevelInfoRouter
import com.layaa.libnet.util.Success
import com.layaa.libui.base.BaseBindingVMFragment
import com.layaa.libui.currencyFormat
import com.layaa.libui.toEnString
import com.layaa.libutils.ClickValidUtil
import com.layaa.libutils.UIUtils
import com.layaa.libutils.ViewUtil
import com.layaa.libutils.dp
import com.layaa.skinlib.Skinable
import com.layaa.widget.GridLayoutManagerWrapper
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import org.json.JSONArray

/**
 *<AUTHOR>
 *@date 2023/9/6
 *@des
 **/
@Skinable
@Route(path = LoginPath.PAGE_MINE_FRAGMENT)
class MineFragment :
    BaseBindingVMFragment<ProfileViewModel, AccountFragmentMineBinding>(),
    LanguageObserve, View.OnClickListener {

    private var hasShowUserInfo = false

    private var curUserInfo: UserProfileEntity? = null

    private val functionAdapter by lazy {
        ProfileFunctionAdapter(requireContext()).apply {
            onItemClickListener = { type ->
                when (type) {
                    R.drawable.setting_icon_account_layer -> {
                        //设置页
                        startActivity(Intent(context, SettingActivity::class.java))
                        true
                    }

                    R.drawable.icon_big_mine_item_about -> {
                        //关于页
                        startActivity(Intent(context, AboutUsActivity::class.java))
                        true
                    }

                    R.drawable.account_icon_profile_family -> {
                        gotoFamily()
                        true
                    }

                    else -> false
                }
            }
        }
    }


    override fun initBinding(
        inflater: LayoutInflater,
        parent: ViewGroup?,
        attachToParent: Boolean
    ): AccountFragmentMineBinding {
        return AccountFragmentMineBinding.inflate(inflater, parent, attachToParent)
    }

    override fun initData() {
        LanguageNotification.addObserve(TAG, this)
        if (!EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().register(this)
        }
    }

    override fun initEvent() {
        binding.bgAvatar.setOnClickListener {
            if (!ClickValidUtil.clickValidShort()) {
                return@setOnClickListener
            }
            curUserInfo?.userCommon?.birthDate?.let { it1 -> ProfileRouter.gotoEditProfile(it1) }
        }

        binding.avatar.setOnClickListener {
            if (!ClickValidUtil.clickValidShort()) {
                return@setOnClickListener
            }
            val bundle = Bundle()
            LoginRouter.gotoProfileActivity(bundle)
        }
        binding.imgLevelHelp.setOnClickListener {
            LevelInfoRouter.gotoUserLevelInfoPage(Bundle())
        }
        binding.leveBgView.setOnClickListener {
            binding.imgLevelHelp.performClick()
        }

        viewModel.userData.observe(this) {
            if (it is Success) {
                updateUserInfo(it.value)
            }
        }

        viewModel.userFunction.observe(this) {
            functionAdapter.refreshList(it)
        }

        viewModel.bannedData.observe(this) {
            if (it is Success) {
                if (it.value.ban) {
                    LoginRouter.logout(BannedData().apply {
                        banText = it.value.banText
                        banType = "0"
                    })

                }
            }
        }
        viewModel.familyData.observe(this) {
            if (it is Success) {
                val myFamilyInfo = it.value
                functionAdapter.updateUnread(
                    R.drawable.account_icon_profile_family,
                    myFamilyInfo.applicationCountString()
                )
            }
        }

        binding.diamondBtn.setOnClickListener {
            if (!ClickValidUtil.clickValidShort()) {
                return@setOnClickListener
            }
            gotoWallet()
        }

        binding.gemBtn.setOnClickListener {
            //暂时一样
            binding.diamondBtn.performClick()
        }

        binding.beanBtn.setOnClickListener {
            //暂时一样
            binding.diamondBtn.performClick()
        }

        binding.goldBtn.setOnClickListener {
            //暂时一样
            binding.diamondBtn.performClick()
        }

        binding.topUp.setOnClickListener {
            gotoWallet()
        }

        binding.friendTitle.setOnClickListener(this)
        binding.followTitle.setOnClickListener(this)
        binding.fansTitle.setOnClickListener(this)
        binding.visitorTitle.setOnClickListener(this)
        binding.friendCount.setOnClickListener(this)
        binding.followCount.setOnClickListener(this)
        binding.fansCount.setOnClickListener(this)
        binding.visitorCount.setOnClickListener(this)

        ViewUtil.expandViewTouchDelegate(binding.friendTitle, UIUtils.getPixels(20f))
        ViewUtil.expandViewTouchDelegate(binding.followTitle, UIUtils.getPixels(20f))
        ViewUtil.expandViewTouchDelegate(binding.fansTitle, UIUtils.getPixels(20f))
        ViewUtil.expandViewTouchDelegate(binding.visitorTitle, UIUtils.getPixels(20f))

        binding.functionList.itemAnimator = null
        binding.functionList.layoutManager = GridLayoutManagerWrapper(requireContext(), 3)
//        binding.functionList.layoutManager = LinearLayoutManager(requireContext())
        binding.functionList.adapter = functionAdapter

        binding.swipeLayout.setOnRefreshListener {
            refreshData()
        }

    }

    override fun refreshData() {
        viewModel.isDeviceBanned()
        viewModel.getUserProfile()
        viewModel.fetchMyFamilyInfo()
        val jsonArray = JSONArray()
        jsonArray.put(WalletConst.WALLET_TYPE_COIN)
        jsonArray.put(WalletConst.WALLET_TYPE_DIAMOND)
        jsonArray.put(WalletConst.WALLET_TYPE_GEM)
        jsonArray.put(WalletConst.WALLET_TYPE_BEAN)
        WalletRouter.getUserBalance(jsonArray)
    }

    override fun onResume() {
        super.onResume()
        if (hasShowUserInfo) {
            refreshData()
            //viewModel.fetchMyFamilyInfo()
        }
    }

    @SuppressLint("SetTextI18n")
    private fun updateUserInfo(userInfo: UserProfileEntity) {
        binding.swipeLayout.isRefreshing = false
        curUserInfo = userInfo
        curUserInfo ?: return

        functionAdapter.refreshList(userInfo.functionList)
        binding.avatar.loadAvatar(userInfo.userCommon?.photo, UIUtils.getPixels(63f))
        binding.avatar.loadHeadgear(userInfo.userCommon?.headWear, 75.dp)

        binding.userLabelsView.vip(userInfo.vipLevel)
            .level(userInfo.userLevel)
            .achievement(userInfo.userCommon.achievementSmallIcons)
            .show()
        userInfo.userCommon?.userLevelDTO?.let {
            binding.txtLevelCurrent.text = "LV.${it.level}"
            if (it.nextExp > it.exp) {
                binding.txtLevelNext.isVisible = true
                binding.txtLevelNext.text = "LV.${it.level + 1}"
                binding.progressLevel.progress =
                    (((it.exp - it.lowExp) / (it.nextExp - it.lowExp).toFloat()) * 100).toInt()
                binding.txtLevelExp.text = "${it.exp - it.lowExp}/${it.nextExp - it.lowExp}"
            } else {
                binding.txtLevelNext.isVisible = false
                binding.progressLevel.progress = 100
                binding.txtLevelExp.text = "${it.exp - it.lowExp}"
            }
        }
        binding.playerId.setAutoUid(
            userInfo.userCommon?.uid,
            userInfo.userCommon?.userPrettyUidDTO?.prettyUid,
            userInfo.userCommon?.userPrettyUidDTO?.expireMs
        )

        binding.nickname.show(userInfo.userCommon?.nicknameBean)

        binding.diamondCount.text = userInfo.accountInfo.diamondNum.currencyFormat()
        binding.goldCount.text = userInfo.accountInfo.goldenNum.currencyFormat()


        when (TextUtils.isEmpty(userInfo.accountInfo.beansNum)) {
            //是否有主播账户
            false -> {
                binding.gemBtn.isVisible = false
                binding.beanBtn.isVisible = true
                binding.beanCount.text = userInfo.accountInfo.beansNum.currencyFormat()
                binding.goldBtn.layoutParams =
                    (binding.goldBtn.layoutParams as ConstraintLayout.LayoutParams).apply {
                        endToStart = binding.beanBtn.id
                    }
            }

            else -> {
                binding.gemBtn.isVisible = true
                binding.beanBtn.isVisible = false
                binding.gemCount.text = userInfo.accountInfo.gemsNum.currencyFormat()
                binding.goldBtn.layoutParams =
                    (binding.goldBtn.layoutParams as ConstraintLayout.LayoutParams).apply {
                        endToStart = binding.gemBtn.id
                    }
            }
        }

        binding.friendCount.text = userInfo.relationAggregate.friends.totalCount.toEnString()
        binding.followCount.text = userInfo.relationAggregate.follows.totalCount.toEnString()
        binding.fansCount.text = userInfo.relationAggregate.fans.totalCount.toEnString()
        binding.visitorCount.text = userInfo.relationAggregate.visitors.totalCount.toEnString()

        if (userInfo.relationAggregate.friends.newRelationCount > 0) {
            binding.friendNewCount.text = "+" + userInfo.relationAggregate.friends.newRelationCount
            binding.friendNewCount.isVisible = true
        } else {
            binding.friendNewCount.isVisible = false
        }
        if (userInfo.relationAggregate.follows.newRelationCount > 0) {
            binding.followNewCount.text = "+" + userInfo.relationAggregate.follows.newRelationCount
            binding.followNewCount.isVisible = true
        } else {
            binding.followNewCount.isVisible = false
        }
        if (userInfo.relationAggregate.fans.newRelationCount > 0) {
            binding.fansNewCount.text = "+" + userInfo.relationAggregate.fans.newRelationCount
            binding.fansNewCount.isVisible = true
        } else {
            binding.fansNewCount.isVisible = false
        }
        if (userInfo.relationAggregate.visitors.newRelationCount > 0) {
            binding.visitorNewCount.text =
                "+" + userInfo.relationAggregate.visitors.newRelationCount
            binding.visitorNewCount.isVisible = true
        } else {
            binding.visitorNewCount.isVisible = false
        }

        hasShowUserInfo = true
    }

    override fun languageObserver(id: Int, vararg objects: Any?) {

    }

    override fun onDestroy() {
        super.onDestroy()
        LanguageNotification.removeObserve(TAG)
        EventBus.getDefault().unregister(this)
        hasShowUserInfo = false
    }

    override fun onClick(v: View?) {
        v ?: return
        if (v.id == R.id.friend_title || v.id == R.id.friend_count) {
            val bundle = Bundle()
            bundle.putString(FriendConst.KEY_FRIEND_POSITION, FriendConst.POSITION_FRIEND)
            gotoFriendList(bundle)
            binding.friendNewCount.isVisible = false
        } else if (v.id == R.id.follow_title || v.id == R.id.follow_count) {
            val bundle = Bundle()
            bundle.putString(FriendConst.KEY_FRIEND_POSITION, FriendConst.POSITION_FOLLOW)
            gotoFriendList(bundle)
            binding.followNewCount.isVisible = false
        } else if (v.id == R.id.fans_title || v.id == R.id.fans_count) {
            val bundle = Bundle()
            bundle.putString(FriendConst.KEY_FRIEND_POSITION, FriendConst.POSITION_FANS)
            gotoFriendList(bundle)
            binding.fansNewCount.isVisible = false
        } else if (v.id == R.id.visitor_title || v.id == R.id.visitor_count) {
            val bundle = Bundle()
            bundle.putString(FriendConst.KEY_FRIEND_POSITION, FriendConst.POSITION_VISITORS)
            gotoFriendList(bundle)
            binding.visitorNewCount.isVisible = false
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onEvent(event: UpdateDiamondEvent) {
        binding.diamondCount.text = event.value.currencyFormat()
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onEvent(event: UpdateGoldEvent) {
        binding.goldCount.text = event.value.currencyFormat()
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onEvent(event: UpdateGemEvent) {
        binding.gemCount.text = event.value.currencyFormat()
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onEvent(event: UpdateBeanEvent) {
        binding.beanCount.text = event.value.currencyFormat()
    }

    @Subscribe
    fun onEvent(updateAvatar: UpdateHeadEvent) {
        curUserInfo?.userCommon?.photo = updateAvatar.photo
        binding.avatar.loadAvatar(updateAvatar.photo, UIUtils.getPixels(99F))
    }

    @Subscribe
    fun onEvent(updateNickname: UpdateNicknameEvent) {
        curUserInfo?.userCommon?.nickName = updateNickname.nickname
        binding.nickname.text = updateNickname.nickname
    }

    private fun gotoFamily() {
        val familyId = UserManager.instance.getUserInfo()?.familyId
        if (familyId?.isNotBlank() == true) {
            FamilyRouter.gotoHomePage(familyId)
        } else {
            FamilyRouter.gotoRankPage()
        }
    }
}