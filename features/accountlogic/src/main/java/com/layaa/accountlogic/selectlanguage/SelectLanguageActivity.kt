package com.layaa.accountlogic.selectlanguage

import android.text.TextUtils
import android.view.LayoutInflater
import com.layaa.accountapi.login.LoginRouter
import com.layaa.accountlogic.api.LoginKtApi
import com.layaa.accountlogic.databinding.AccountActivitySelectLanguageBinding
import com.layaa.language.LanguageController
import com.layaa.libnet.RetrofitFactory
import com.layaa.libui.R
import com.layaa.libui.base.BaseBindingVMActivity
import com.layaa.widget.mvvm.BaseViewModel
import com.layaa.libui.widget.LinearLayoutManagerWrapper
import com.layaa.libutils.ClickValidUtil
import com.layaa.libutils.NetUtils
import com.layaa.libutils.toast.ToastUtils
import com.layaa.skinlib.SkinKit
import kotlinx.coroutines.DelicateCoroutinesApi
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch

/**
 *<AUTHOR>
 *@date 2023/11/29
 *@des
 **/
class SelectLanguageActivity :
    BaseBindingVMActivity<BaseViewModel, AccountActivitySelectLanguageBinding>() {

    companion object {
        @OptIn(DelicateCoroutinesApi::class)
        @JvmStatic
        fun updateLanguage() {
            GlobalScope.launch(Dispatchers.IO) {
                try {
                    val params = HashMap<String, String>()
                    params["lang"] = LanguageController.getLanguage()
                    params["uid"] = LoginRouter.getUserId()
                    RetrofitFactory.getApi(LoginKtApi::class.java).updateUserProfile(params)
                } catch (e: Exception) {
                    e.printStackTrace()
                }

            }
        }
    }

    private val mLanguageList = ArrayList<LanguageBean>()

    private val adapter: SelectLanguageAdapter by lazy {
        SelectLanguageAdapter(this).apply {
            setOnClickListener { v ->
                if (!ClickValidUtil.clickValidNormal()) {
                    return@setOnClickListener
                }
                val bean = v.tag as LanguageBean ?: return@setOnClickListener
                selectLanguage(bean)
            }
        }
    }

    override fun inflateLayout(layoutInflater: LayoutInflater): AccountActivitySelectLanguageBinding {
        return AccountActivitySelectLanguageBinding.inflate(layoutInflater)
    }

    override fun initEvent() {

        binding.back.setOnClickListener {
            finish()
        }
        binding.languageList.layoutManager = LinearLayoutManagerWrapper(this)
        binding.languageList.itemAnimator = null
        binding.languageList.adapter = adapter

        LanguageController.supportedLanguages.forEach {
            val bean = LanguageBean()
            bean.simple = it.key
            bean.language = it.value
            bean.isSelected = it.key == LanguageController.getLanguage()
            mLanguageList.add(bean)
        }
        adapter.refreshList(mLanguageList)

        binding.done.setOnClickListener {
            if (!ClickValidUtil.clickValidNormal()) {
                return@setOnClickListener
            }
            if (!NetUtils.isNetworkAvailable()) {
                ToastUtils.show(
                    getString(R.string.vr_network_unavailable)
                )
                return@setOnClickListener
            }

            var languageCode = "en"
            var languageName = "English"
            for (bean in mLanguageList) {
                if (bean.isSelected) {
                    languageCode = bean.simple
                    languageName = bean.language
                    break
                }
            }
            // 如果语言没变 不请求
            if (TextUtils.equals(languageCode, LanguageController.getLanguage())) {
                finish()
                return@setOnClickListener
            }
            LanguageController.changeLanguage(this, languageCode, false)
            SkinKit.getInstance().setConfig(
                SkinKit.getInstance().configBuilder
                    .setRtl(LanguageController.isRtl())
                    .setLang(LanguageController.getLanguage())
                    .build()
            )
            updateLanguage()
            finish()
        }
    }

    private fun selectLanguage(bean: LanguageBean) {
        for (b in mLanguageList) {
            b.isSelected = TextUtils.equals(b.language, bean.language)
        }
        adapter.refreshList(mLanguageList)
    }

    override fun isStatusBarFontDarkColor(): Boolean {
        return true
    }
}