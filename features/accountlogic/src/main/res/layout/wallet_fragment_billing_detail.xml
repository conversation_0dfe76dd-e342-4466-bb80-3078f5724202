<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <RelativeLayout
        android:id="@+id/typeView"
        android:layout_width="match_parent"
        android:layout_height="36dp"
        android:background="#F7F8FA"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            android:id="@+id/bill_detail_type"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentEnd="true"
            android:layout_centerVertical="true"
            android:layout_marginEnd="15dp"
            android:drawableEnd="@drawable/icon_down_arrow"
            android:drawablePadding="3dp"
            android:text="@string/bill_type_all"
            android:textColor="#202530"
            android:textSize="13sp"
            app:fontFamily="@font/montserrat_medium" />

        <TextView
            android:id="@+id/bill_detail_date"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_marginStart="15dp"
            android:drawableEnd="@drawable/icon_down_arrow"
            android:drawablePadding="3dp"
            android:text="@string/bill_month"
            android:textColor="#202530"
            android:textSize="13sp"
            app:fontFamily="@font/montserrat_medium" />

    </RelativeLayout>

    <TextView
        android:id="@+id/restore_purchases"
        android:layout_width="wrap_content"
        android:layout_height="35dp"
        android:layout_marginEnd="15dp"
        android:gravity="center"
        android:text="@string/restore_purchases"
        android:textColor="#1FB58D"
        android:textSize="14sp"
        android:textStyle=""
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/typeView" />

    <com.layaa.widget.xrecyclerview.XRecyclerView
        android:id="@+id/rv_detail"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintHeight_max="0dp"
        app:layout_constraintTop_toBottomOf="@id/restore_purchases" />


    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:visibility="visible"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/typeView">

        <com.layaa.libui.widget.EmptyView
            android:id="@+id/txt_empty"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="100dp"
            android:visibility="gone"
            app:iconHeight="150dp"
            app:iconWidth="150dp"
            app:textSize="12sp" />

        <include
            android:id="@+id/layout_error"
            layout="@layout/account_layout_net_error"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:visibility="gone" />

    </FrameLayout>


</androidx.constraintlayout.widget.ConstraintLayout>