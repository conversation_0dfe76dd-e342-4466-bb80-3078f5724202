<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginHorizontal="5dp"
    android:layout_marginTop="10dp"
    tools:layout_width="120dp">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintDimensionRatio="109:91"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        android:background="@drawable/bg_12_f7f8fa">

        <ImageView
            android:id="@+id/icon_coins_iv"
            android:layout_width="21dp"
            android:layout_height="21dp"
            android:layout_marginTop="21dp"
            android:layout_marginEnd="1dp"
            app:srcReverse="true"
            android:src="@drawable/wallet_icon_cash"
            app:layout_constraintEnd_toStartOf="@+id/value_tv"
            app:layout_constraintHorizontal_chainStyle="packed"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/value_tv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="2dp"
            android:textColor="#FF202530"
            android:textSize="16sp"
            app:fontFamily="@font/montserrat_bold"
            app:layout_constraintBottom_toBottomOf="@+id/icon_coins_iv"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_chainStyle="packed"
            app:layout_constraintStart_toEndOf="@+id/icon_coins_iv"
            app:layout_constraintTop_toTopOf="@+id/icon_coins_iv"
            tools:text="2500" />

        <LinearLayout
            android:id="@+id/layout_exchange"
            android:layout_width="match_parent"
            android:layout_height="28dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            android:orientation="horizontal"
            android:gravity="center"
            tools:background="@drawable/wallet_bg_charge_item_price_cash">

            <ImageView
                android:id="@+id/diamond_icon"
                android:layout_width="15dp"
                android:layout_height="15dp"
                android:layout_marginEnd="1dp"
                app:srcReverse="true"
                tools:src="@drawable/wallet_icon_beans" />

            <TextView
                android:id="@+id/request_damon_tv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="#FFFFFF"
                android:textSize="13sp"
                app:fontFamily="@font/montserrat_medium"
                tools:text="100" />

        </LinearLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>