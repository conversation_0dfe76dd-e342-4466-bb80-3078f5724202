<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#fff">

    <com.layaa.widget.TitleStatusBarView
        android:id="@+id/titleBar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="parent">

        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="44dp">

            <ImageView
                android:id="@+id/back"
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:layout_gravity="center_vertical|start"
                android:layout_marginStart="9dp"
                android:scaleType="centerCrop"
                app:srcReverse="true"
                android:src="@drawable/account_title_back_icon" />

            <TextView
                android:id="@+id/titleTxt"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:text="@string/settings_phone_bind_page_title"
                app:fontFamily="@font/montserrat_medium"
                android:textColor="@color/color_title_bar_text"
                android:textSize="16sp" />
        </FrameLayout>
    </com.layaa.widget.TitleStatusBarView>

    <TextView
        android:id="@+id/tipsNumber"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="15dp"
        android:layout_marginTop="28dp"
        android:text="@string/login_code_main_title"
        android:textColor="#FF242323"
        android:textSize="22sp"
        app:fontFamily="@font/montserrat_bold"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/titleBar" />

    <TextView
        android:id="@+id/phoneNumberTitle"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:layout_marginEnd="15dp"
        app:layout_constraintStart_toStartOf="@+id/tipsNumber"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tipsNumber"
        android:textColor="@color/color_title_bar_text"
        android:textSize="13sp"
        android:text="@string/account_tips_send_to_phone" />

<!--    <TextView-->
<!--        android:id="@+id/phoneNumber"-->
<!--        android:layout_width="wrap_content"-->
<!--        android:layout_height="wrap_content"-->
<!--        app:layout_constraintTop_toTopOf="@id/phoneNumberTitle"-->
<!--        app:layout_constraintBottom_toBottomOf="@id/phoneNumberTitle"-->
<!--        app:layout_constraintStart_toEndOf="@id/phoneNumberTitle"-->
<!--        android:textColor="@color/app_color"-->
<!--        android:textSize="13sp"-->
<!--        app:fontFamily="@font/montserrat_semi_bold"-->
<!--        android:layout_marginStart="2dp"-->
<!--        tools:text="+********" />-->

    <com.layaa.accountlogic.widget.InputCodeView
        android:id="@+id/inputCode"
        android:layout_width="0dp"
        android:layout_height="46dp"
        android:layout_marginHorizontal="42dp"
        android:layout_marginTop="22dp"
        android:textColor="#FF242323"
        android:textSize="40sp"
        android:maxLength="6"
        android:inputType="number"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/phoneNumberTitle"
        app:line_margin="23dp" />

    <TextView
        android:id="@+id/errorTips"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:textColor="#FF5470"
        android:textSize="13sp"
        android:visibility="gone"
        app:layout_constraintStart_toStartOf="@+id/inputCode"
        app:layout_constraintTop_toBottomOf="@+id/inputCode" />

    <TextView
        android:id="@+id/next"
        android:layout_width="0dp"
        android:layout_height="42dp"
        android:layout_marginTop="38dp"
        android:alpha="0.3"
        android:background="@drawable/account_bg_confirm"
        android:enabled="false"
        android:gravity="center"
        android:text="@string/login_code_resend_button"
        android:textColor="#fff"
        android:textSize="15sp"
        app:fontFamily="@font/montserrat_semi_bold"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_marginHorizontal="15dp"
        app:layout_constraintTop_toBottomOf="@+id/inputCode" />

</androidx.constraintlayout.widget.ConstraintLayout>