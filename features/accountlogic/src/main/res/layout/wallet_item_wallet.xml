<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_marginHorizontal="15dp"
    android:layout_marginTop="9dp">

    <ImageView
        android:id="@+id/icon"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintDimensionRatio="345:90"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:srcReverse="true"
        tools:src="@drawable/wallet_bg_diamond" />

    <TextView
        android:id="@+id/num"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="15dp"
        android:textColor="#FFFFFF"
        android:textSize="20sp"
        android:textStyle="bold"
        tools:text="99,999,990"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toTopOf="@id/name"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintVertical_chainStyle="packed" />

    <TextView
        android:id="@+id/name"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="#FFFFFF"
        android:textSize="12sp"
        app:layout_constraintStart_toStartOf="@id/num"
        app:layout_constraintTop_toBottomOf="@id/num"
        app:layout_constraintBottom_toBottomOf="parent"
        tools:text="Diamond" />

    <TextView
        android:id="@+id/action"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="9dp"
        android:paddingHorizontal="9dp"
        android:paddingVertical="3.5dp"
        android:background="@drawable/wallet_bg_top_up_item"
        android:gravity="center"
        android:textColor="#FF202530"
        android:textSize="12sp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>