<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/rootView"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <ImageView
        android:id="@+id/imgBg"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:adjustViewBounds="true"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:src="@drawable/wallet_bg_recharge_diamond" />

    <com.layaa.widget.TitleStatusBarView
        android:id="@+id/title_bar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="44dp">

            <ImageView
                android:id="@+id/back"
                android:layout_width="44dp"
                android:layout_height="44dp"
                android:padding="10dp"
                android:scaleType="center"
                android:src="@drawable/account_title_back_icon_white"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:srcReverse="true"
                tools:ignore="MissingPrefix" />

            <TextView
                android:id="@+id/titleTv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/recharge"
                android:textColor="#FFFFFF"
                android:textSize="16sp"
                android:textStyle="bold"
                app:fontFamily="@font/montserrat_medium"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/bill_details"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/bill_details_title"
                android:textColor="#FFFFFF"
                android:textSize="14sp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                android:layout_marginEnd="15dp"
                app:layout_goneMarginEnd="24dp" />

        </androidx.constraintlayout.widget.ConstraintLayout>
    </com.layaa.widget.TitleStatusBarView>

    <ImageView
        android:id="@+id/imgIcon"
        android:layout_width="120dp"
        android:layout_height="120dp"
        tools:src="@drawable/wallet_icon_diamond_big"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/title_bar"
        android:layout_marginTop="16dp" />

    <TextView
        android:id="@+id/txtAccountTitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="15dp"
        android:layout_marginTop="16dp"
        android:textColor="#E6FFFFFF"
        android:textSize="12sp"
        android:text="@string/balance"
        app:layout_constraintStart_toStartOf="@id/title_bar"
        app:layout_constraintTop_toBottomOf="@id/title_bar" />


    <TextView
        android:id="@+id/txtExchangeBalance"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="#FFFFFFFF"
        android:textSize="24sp"
        app:fontFamily="@font/montserrat_bold"
        tools:text="3,999,392"
        android:layout_marginTop="15dp"
        app:layout_constraintTop_toBottomOf="@id/txtAccountTitle"
        app:layout_constraintStart_toStartOf="@id/txtAccountTitle" />


    <LinearLayout
        android:id="@+id/layoutAccountBalance"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toBottomOf="@id/title_bar"
        android:layout_marginTop="12dp"
        app:layout_constraintEnd_toEndOf="parent"
        android:orientation="horizontal"
        android:paddingVertical="1.5dp"
        android:paddingHorizontal="9dp"
        android:gravity="center"
        android:background="@drawable/wallet_bg_exchange_diamond_balance">

        <ImageView
            android:id="@+id/imgAccountIcon"
            android:layout_width="18dp"
            android:layout_height="18dp"
            android:layout_marginEnd="3dp"
            app:srcReverse="true"
            tools:src="@drawable/wallet_icon_beans" />

        <TextView
            android:id="@+id/txtAccountBalance"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:textColor="#FFFFFF"
            android:textSize="13sp"
            tools:text="1000"
            app:fontFamily="@font/montserrat_semi_bold" />
    </LinearLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/txtExchangeBalance"
        app:layout_constraintBottom_toBottomOf="parent"
        android:layout_marginTop="21dp"
        android:background="@drawable/bg_top_24_white">

        <TextView
            android:id="@+id/txtBeanTitle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="15dp"
            android:layout_marginTop="15dp"
            android:text="@string/my_gold_bean"
            android:textColor="#FF202530"
            android:textSize="18sp"
            android:visibility="gone"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:fontFamily="@font/montserrat_medium" />

        <TextView
            android:id="@+id/txtBeanTips"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="6dp"
            android:gravity="start"
            android:layout_marginHorizontal="15dp"
            android:text="@string/tips_select_exchange_mode"
            android:textColor="#FF4E5969"
            android:textSize="13sp"
            android:visibility="gone"
            app:layout_constraintTop_toBottomOf="@id/txtBeanTitle" />


        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/list"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="10.5dp"
            android:overScrollMode="never"
            app:layout_constraintTop_toBottomOf="@+id/txtBeanTips" />

        <TextView
            android:id="@+id/txtDesc"
            app:layout_constraintTop_toBottomOf="@id/list"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="15dp"
            android:layout_marginHorizontal="15dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            android:visibility="gone"
            tools:visibility="visible"
            android:text="@string/wallet_desc_coin" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>