<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_marginTop="10dp">

    <View
        android:id="@+id/bg_view"
        android:layout_width="match_parent"
        android:layout_height="52dp"
        android:layout_marginHorizontal="4.5dp"
        android:background="@drawable/bg_6_f7f8fa"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_margin="9dp"
        android:textColor="#FF202530"
        android:textSize="14sp"
        app:layout_constraintStart_toStartOf="@+id/bg_view"
        app:layout_constraintTop_toTopOf="@+id/bg_view"
        app:layout_constraintBottom_toBottomOf="@id/bg_view"
        app:layout_constraintEnd_toStartOf="@id/count"
        app:layout_constraintHorizontal_bias="0"
        app:layout_constrainedWidth="true"
        tools:text="Receive gift money in the voice roomroom" />

    <TextView
        android:id="@+id/count"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="3dp"
        android:textColor="#FF202530"
        android:textSize="13sp"
        tools:text="123343"
        app:fontFamily="@font/montserrat_semi_bold"
        app:layout_constraintBottom_toBottomOf="@+id/title"
        app:layout_constraintEnd_toStartOf="@+id/icon"
        app:layout_constraintTop_toTopOf="@+id/title" />

    <ImageView
        android:id="@+id/icon"
        android:layout_width="20dp"
        android:layout_height="20dp"
        android:layout_marginEnd="3dp"
        app:layout_constraintBottom_toBottomOf="@+id/title"
        app:layout_constraintEnd_toStartOf="@+id/arrow"
        app:layout_constraintTop_toTopOf="@+id/title"
        app:srcReverse="true"
        tools:src="@drawable/wallet_icon_beans" />

    <ImageView
        android:id="@+id/arrow"
        android:layout_width="12dp"
        android:layout_height="12dp"
        android:layout_marginEnd="9dp"
        app:layout_constraintBottom_toBottomOf="@+id/title"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@+id/title"
        tools:src="@drawable/wallet_icon_arrow_up" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/item_list"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:focusable="false"
        android:focusableInTouchMode="false"
        android:overScrollMode="never"
        app:layout_constraintTop_toBottomOf="@+id/bg_view" />

</androidx.constraintlayout.widget.ConstraintLayout>