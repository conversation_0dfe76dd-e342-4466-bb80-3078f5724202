<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/mine_bg_main_tab">

    <ImageView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:adjustViewBounds="true"
        android:src="@drawable/mine_bg_main_tab_top"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <com.layaa.widget.TitleStatusBarView
        android:id="@+id/title_bar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="0dp">

        </androidx.constraintlayout.widget.ConstraintLayout>
    </com.layaa.widget.TitleStatusBarView>

    <androidx.swiperefreshlayout.widget.SwipeRefreshLayout
        android:id="@+id/swipeLayout"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/title_bar">

        <ScrollView
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:descendantFocusability="blocksDescendants"
            android:fillViewport="true"
            android:focusable="true"
            android:focusableInTouchMode="true">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:paddingBottom="20dp">

                <View
                    android:id="@+id/bg_avatar"
                    android:layout_width="match_parent"
                    android:layout_height="0dp"
                    app:layout_constraintBottom_toBottomOf="@+id/avatar"
                    app:layout_constraintTop_toTopOf="@+id/avatar" />

                <com.layaa.widget.AvatarView
                    android:id="@+id/avatar"
                    android:layout_width="75dp"
                    android:layout_height="75dp"
                    android:layout_marginTop="15dp"
                    android:scaleType="centerCrop"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    tools:background="@drawable/icon_def_avatar" />

                <com.layaa.libui.widget.nickname.NicknameTextView
                    android:id="@+id/nickname"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="90dp"
                    android:layout_marginTop="3dp"
                    android:ellipsize="end"
                    android:gravity="center"
                    android:maxLines="1"
                    android:textColor="#FFFFFFFF"
                    android:textSize="18sp"
                    app:fontFamily="@font/montserrat_bold"
                    app:gradientOrientation="horizontal"
                    app:layout_constrainedWidth="true"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/avatar"
                    app:layout_constraintVertical_chainStyle="packed"
                    tools:text="一二三四五六七八九" />

                <com.layaa.libui.widget.PrettyUidView
                    android:id="@+id/player_id"
                    android:layout_width="wrap_content"
                    android:layout_height="21dp"
                    android:layout_marginStart="8dp"
                    android:layout_marginTop="7dp"
                    android:textColor="#FFFFFFFF"
                    android:textSize="13sp"
                    app:idColor="@color/color_white"
                    app:isDialogExpTime="true"
                    app:layout_constraintEnd_toEndOf="@id/nickname"
                    app:layout_constraintStart_toStartOf="@id/nickname"
                    app:layout_constraintTop_toBottomOf="@id/userLabelsView"
                    app:layout_constraintVertical_chainStyle="packed" />

                <com.layaa.libui.widget.UserLabelsView
                    android:id="@+id/userLabelsView"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="3dp"
                    app:layout_constraintEnd_toEndOf="@id/nickname"
                    app:layout_constraintStart_toStartOf="@id/nickname"
                    app:layout_constraintTop_toBottomOf="@id/nickname" />

                <ImageView
                    android:layout_width="15dp"
                    android:layout_height="15dp"
                    android:layout_marginTop="39dp"
                    android:layout_marginEnd="30dp"
                    android:src="@drawable/account_icon_profile_arrow"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="@+id/avatar"
                    app:srcReverse="true" />

                <TextView
                    android:id="@+id/friend_count"
                    android:layout_width="0dp"
                    android:layout_height="24dp"
                    android:layout_marginTop="21dp"
                    android:gravity="center"
                    android:textColor="#ffffff"
                    android:textSize="14sp"
                    app:fontFamily="@font/montserrat_bold"
                    app:layout_constraintEnd_toEndOf="@+id/friend_title"
                    app:layout_constraintStart_toStartOf="@+id/friend_title"
                    app:layout_constraintTop_toBottomOf="@+id/player_id"
                    tools:text="0" />

                <TextView
                    android:id="@+id/friend_title"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/profile_page_friends_entrance"
                    android:textColor="#FFFFFF"
                    android:textSize="10sp"
                    app:layout_constraintEnd_toStartOf="@+id/follow_title"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/friend_count" />

                <TextView
                    android:id="@+id/friend_new_count"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="-3dp"
                    android:background="@drawable/mine_bg_main_tab_unread"
                    android:paddingHorizontal="3dp"
                    android:paddingVertical="1.5dp"
                    android:textColor="#FFFFFF"
                    android:textSize="10sp"
                    android:visibility="gone"
                    app:fontFamily="@font/montserrat_bold"
                    app:layout_constraintStart_toEndOf="@+id/friend_count"
                    app:layout_constraintTop_toTopOf="@+id/friend_count"
                    tools:ignore="SmallSp"
                    tools:text="99"
                    tools:visibility="visible" />

                <TextView
                    android:id="@+id/follow_count"
                    android:layout_width="0dp"
                    android:layout_height="24dp"
                    android:gravity="center"
                    android:textColor="#ffffff"
                    android:textSize="14sp"
                    app:fontFamily="@font/montserrat_bold"
                    app:layout_constraintEnd_toEndOf="@+id/follow_title"
                    app:layout_constraintStart_toStartOf="@+id/follow_title"
                    app:layout_constraintTop_toTopOf="@+id/friend_count"
                    tools:text="0" />

                <TextView
                    android:id="@+id/follow_title"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/profile_page_following_entrance"
                    android:textColor="#FFFFFF"
                    android:textSize="10sp"
                    app:layout_constraintEnd_toStartOf="@+id/fans_title"
                    app:layout_constraintStart_toEndOf="@+id/friend_title"
                    app:layout_constraintTop_toTopOf="@+id/friend_title"
                    tools:ignore="SmallSp" />

                <TextView
                    android:id="@+id/follow_new_count"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="-3dp"
                    android:background="@drawable/mine_bg_main_tab_unread"
                    android:paddingHorizontal="3dp"
                    android:paddingVertical="1.5dp"
                    android:textColor="#FFFFFF"
                    android:textSize="10sp"
                    android:visibility="gone"
                    app:fontFamily="@font/montserrat_bold"
                    app:layout_constraintStart_toEndOf="@+id/follow_count"
                    app:layout_constraintTop_toTopOf="@+id/follow_count"
                    tools:ignore="SmallSp"
                    tools:text="99"
                    tools:visibility="visible" />

                <TextView
                    android:id="@+id/fans_count"
                    android:layout_width="0dp"
                    android:layout_height="24dp"
                    android:gravity="center"
                    android:textColor="#ffffff"
                    android:textSize="14sp"
                    app:fontFamily="@font/montserrat_bold"
                    app:layout_constraintEnd_toEndOf="@+id/fans_title"
                    app:layout_constraintStart_toStartOf="@+id/fans_title"
                    app:layout_constraintTop_toTopOf="@+id/friend_count"
                    tools:text="0" />

                <TextView
                    android:id="@+id/fans_title"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/profile_page_fans_entrance"
                    android:textColor="#FFFFFF"
                    android:textSize="10sp"
                    app:layout_constraintEnd_toStartOf="@+id/visitor_count"
                    app:layout_constraintStart_toEndOf="@+id/follow_title"
                    app:layout_constraintTop_toTopOf="@+id/friend_title" />

                <TextView
                    android:id="@+id/fans_new_count"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="-3dp"
                    android:background="@drawable/mine_bg_main_tab_unread"
                    android:paddingHorizontal="3dp"
                    android:paddingVertical="1.5dp"
                    android:textColor="#FFFFFF"
                    android:textSize="10sp"
                    android:visibility="gone"
                    app:fontFamily="@font/montserrat_bold"
                    app:layout_constraintStart_toEndOf="@+id/fans_count"
                    app:layout_constraintTop_toTopOf="@+id/fans_count"
                    tools:ignore="SmallSp"
                    tools:text="99"
                    tools:visibility="visible" />

                <TextView
                    android:id="@+id/visitor_count"
                    android:layout_width="0dp"
                    android:layout_height="24dp"
                    android:gravity="center"
                    android:textColor="#ffffff"
                    android:textSize="14sp"
                    app:fontFamily="@font/montserrat_bold"
                    app:layout_constraintEnd_toEndOf="@+id/visitor_title"
                    app:layout_constraintStart_toStartOf="@+id/visitor_title"
                    app:layout_constraintTop_toTopOf="@+id/friend_count"
                    tools:text="0" />

                <TextView
                    android:id="@+id/visitor_title"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/profile_page_visitors_entrance"
                    android:textColor="#FFFFFF"
                    android:textSize="10sp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toEndOf="@+id/fans_title"
                    app:layout_constraintTop_toTopOf="@+id/friend_title" />

                <TextView
                    android:id="@+id/visitor_new_count"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="-3dp"
                    android:background="@drawable/mine_bg_main_tab_unread"
                    android:paddingHorizontal="3dp"
                    android:paddingVertical="1.5dp"
                    android:textColor="#FFFFFF"
                    android:textSize="10sp"
                    android:visibility="gone"
                    app:fontFamily="@font/montserrat_bold"
                    app:layout_constraintStart_toEndOf="@+id/visitor_count"
                    app:layout_constraintTop_toTopOf="@+id/visitor_count"
                    tools:ignore="SmallSp"
                    tools:text="99"
                    tools:visibility="visible" />


                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/layoutWallet"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="15dp"
                    android:layout_marginTop="21dp"
                    android:background="@drawable/mine_bg_main_tab_wallet"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/friend_title">


                    <com.google.android.material.progressindicator.LinearProgressIndicator
                        android:id="@+id/progressLevel"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="6dp"
                        android:layout_marginTop="15dp"
                        android:layout_marginEnd="-6dp"
                        app:indicatorColor="#FFFFF2D9"
                        app:layout_constraintEnd_toStartOf="@id/topUp"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        app:trackColor="#1F000000"
                        app:trackCornerRadius="6dp"
                        app:trackThickness="6dp"
                        tools:progress="20" />

                    <TextView
                        android:id="@+id/txtLevelCurrent"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="6dp"
                        android:layout_marginTop="6dp"
                        android:textColor="@color/color_white"
                        android:textSize="10sp"
                        app:fontFamily="@font/montserrat_bold"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/progressLevel"
                        tools:ignore="SmallSp"
                        tools:text="LV.3" />

                    <TextView
                        android:id="@+id/txtLevelExp"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="6dp"
                        android:layout_marginTop="6dp"
                        android:textColor="@color/color_white"
                        android:textSize="10sp"
                        app:layout_constraintEnd_toEndOf="@id/progressLevel"
                        app:layout_constraintStart_toStartOf="@id/progressLevel"
                        app:layout_constraintTop_toBottomOf="@id/progressLevel"
                        tools:ignore="SmallSp"
                        tools:text="100/3000" />

                    <TextView
                        android:id="@+id/txtLevelNext"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="6dp"
                        android:layout_marginTop="6dp"
                        android:textColor="@color/color_white"
                        android:textSize="10sp"
                        app:fontFamily="@font/montserrat_bold"
                        app:layout_constraintEnd_toEndOf="@id/progressLevel"
                        app:layout_constraintTop_toBottomOf="@id/progressLevel"
                        tools:ignore="SmallSp"
                        tools:text="LV.4" />


                    <FrameLayout
                        android:id="@+id/topUp"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="12dp"
                        android:layout_marginEnd="6dp"
                        android:background="@drawable/mine_bg_main_tab_wallet_content_top"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toTopOf="parent">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="end"
                            android:layout_marginTop="9dp"
                            android:layout_marginEnd="9dp"
                            android:layout_marginBottom="6dp"
                            android:background="@drawable/mine_bg_main_tab_top_up"
                            android:gravity="center"
                            android:paddingHorizontal="9dp"
                            android:paddingVertical="5dp"
                            android:text="@string/recharge"
                            android:textColor="#E6FFFFFF"
                            android:textSize="12sp"
                            app:fontFamily="@font/montserrat_bold"
                            app:layout_constraintBottom_toBottomOf="@+id/my_wallet"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintTop_toTopOf="@+id/my_wallet" />
                    </FrameLayout>

                    <View
                        android:id="@+id/leveBgView"
                        android:layout_width="0dp"
                        android:layout_height="0dp"
                        app:layout_constraintBottom_toBottomOf="@id/topUp"
                        app:layout_constraintEnd_toEndOf="@id/imgLevelHelp"
                        app:layout_constraintStart_toStartOf="@id/progressLevel"
                        app:layout_constraintTop_toTopOf="parent" />

                    <ImageView
                        android:id="@+id/imgLevelHelp"
                        android:layout_width="15dp"
                        android:layout_height="15dp"
                        android:layout_marginStart="3dp"
                        app:srcReverse="true"
                        android:src="@drawable/mine_icon_main_tab_level_help"
                        app:layout_constraintBottom_toBottomOf="@id/progressLevel"
                        app:layout_constraintStart_toEndOf="@id/progressLevel"
                        app:layout_constraintTop_toTopOf="@id/progressLevel" />

                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginHorizontal="6dp"
                        android:layout_marginBottom="6dp"
                        android:background="@drawable/mine_bg_main_tab_wallet_content_foot"
                        android:paddingHorizontal="12dp"
                        android:paddingVertical="20dp"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/topUp">


                        <LinearLayout
                            android:id="@+id/diamond_btn"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:gravity="center"
                            android:orientation="horizontal"
                            android:visibility="visible"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintEnd_toStartOf="@id/gold_btn"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toTopOf="parent">

                            <ImageView
                                android:id="@+id/diamond_icon"
                                android:layout_width="24dp"
                                android:layout_height="24dp"
                                android:scaleType="centerCrop"
                                android:src="@drawable/wallet_icon_diamond"
                                app:srcReverse="true"
                                android:visibility="visible" />

                            <TextView
                                android:id="@+id/diamond_count"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginStart="3dp"
                                android:drawablePadding="0.5dp"
                                android:ellipsize="end"
                                android:gravity="center_vertical"
                                android:maxLines="1"
                                android:textColor="#FF202530"
                                android:textSize="15sp"
                                app:drawableEndCompat="@drawable/mine_icon_main_tab_wallet_item_arrow"
                                app:fontFamily="@font/montserrat_semi_bold"
                                tools:text="8899900" />

                        </LinearLayout>


                        <LinearLayout
                            android:id="@+id/gold_btn"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:gravity="center"
                            android:orientation="horizontal"
                            android:visibility="visible"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintEnd_toStartOf="@id/bean_btn"
                            app:layout_constraintStart_toEndOf="@id/diamond_btn"
                            app:layout_constraintTop_toTopOf="parent">

                            <ImageView
                                android:id="@+id/gold_icon"
                                android:layout_width="24dp"
                                android:layout_height="24dp"
                                android:scaleType="centerCrop"
                                android:src="@drawable/wallet_icon_coins"
                                android:visibility="visible" />

                            <TextView
                                android:id="@+id/gold_count"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginStart="3dp"
                                android:drawablePadding="0.5dp"
                                android:ellipsize="end"
                                android:gravity="center_vertical"
                                android:maxLines="1"
                                android:textColor="#FF202530"
                                android:textSize="15sp"
                                app:drawableEndCompat="@drawable/mine_icon_main_tab_wallet_item_arrow"
                                app:fontFamily="@font/montserrat_semi_bold"
                                tools:text="8899900" />

                        </LinearLayout>

                        <LinearLayout
                            android:id="@+id/bean_btn"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:gravity="center"
                            android:orientation="horizontal"
                            android:visibility="gone"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintStart_toEndOf="@id/gold_btn"
                            app:layout_constraintTop_toTopOf="parent"
                            tools:visibility="visible">

                            <ImageView
                                android:id="@+id/bean_icon"
                                android:layout_width="24dp"
                                android:layout_height="24dp"
                                android:scaleType="centerCrop"
                                android:src="@drawable/wallet_icon_beans"
                                app:srcReverse="true"
                                android:visibility="visible" />

                            <TextView
                                android:id="@+id/bean_count"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginStart="3dp"
                                android:drawablePadding="0.5dp"
                                android:ellipsize="end"
                                android:gravity="center_vertical"
                                android:maxLines="1"
                                android:textColor="#FF202530"
                                android:textSize="15sp"
                                app:drawableEndCompat="@drawable/mine_icon_main_tab_wallet_item_arrow"
                                app:fontFamily="@font/montserrat_semi_bold"
                                tools:text="8899900" />

                        </LinearLayout>

                        <LinearLayout
                            android:id="@+id/gemBtn"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:gravity="center"
                            android:orientation="horizontal"
                            android:visibility="gone"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintStart_toEndOf="@id/gold_btn"
                            app:layout_constraintTop_toTopOf="parent"
                            tools:visibility="visible">

                            <ImageView
                                android:id="@+id/gemIcon"
                                android:layout_width="24dp"
                                android:layout_height="24dp"
                                android:layout_marginTop="2dp"
                                android:scaleType="centerCrop"
                                android:src="@drawable/wallet_icon_gems"
                                android:visibility="visible" />

                            <TextView
                                android:id="@+id/gemCount"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginStart="3dp"
                                android:drawablePadding="0.5dp"
                                android:ellipsize="end"
                                android:gravity="center_vertical"
                                android:maxLines="1"
                                android:textColor="#FF202530"
                                android:textSize="15sp"
                                app:drawableEndCompat="@drawable/mine_icon_main_tab_wallet_item_arrow"
                                app:fontFamily="@font/montserrat_semi_bold"
                                tools:text="8899900" />

                        </LinearLayout>

                    </androidx.constraintlayout.widget.ConstraintLayout>

                </androidx.constraintlayout.widget.ConstraintLayout>


                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/function_list"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="9dp"
                    android:layout_marginTop="6dp"
                    android:focusable="false"
                    android:focusableInTouchMode="false"
                    android:overScrollMode="never"
                    app:layout_constraintTop_toBottomOf="@+id/layoutWallet" />

            </androidx.constraintlayout.widget.ConstraintLayout>
        </ScrollView>

    </androidx.swiperefreshlayout.widget.SwipeRefreshLayout>

</androidx.constraintlayout.widget.ConstraintLayout>



