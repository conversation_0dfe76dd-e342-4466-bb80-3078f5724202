package com.layaa.roomlogic.module.helper

import android.graphics.Color
import android.net.Uri
import android.os.Message
import android.text.TextUtils
import androidx.core.graphics.toColorInt
import com.google.gson.reflect.TypeToken
import com.layaa.accountapi.login.LoginRouter
import com.layaa.accountapi.uploadfile.UploadFileRouter
import com.layaa.chatapi.ChatRouter
import com.layaa.chatapi.ImEvent
import com.layaa.chatapi.ImEvent.ChannelMsg.LIVE_HEART
import com.layaa.chatapi.im.IMSendCallback
import com.layaa.chatapi.observer.EventMsgModifiedObserver
import com.layaa.chatapi.observer.EventMsgRevokedObserver
import com.layaa.chatapi.observer.EventRoomObserver
import com.layaa.emote.entity.EmoteEntity
import com.layaa.im.bean.ChatType
import com.layaa.im.bean.DataType
import com.layaa.im.bean.MessageStatus
import com.layaa.im.bean.MessageTypedContent
import com.layaa.im.bean.MessageTypedEnvelope
import com.layaa.im.bean.MessageVisibility
import com.layaa.im.bean.data.EmojiMessageData
import com.layaa.im.bean.data.HeartbeatMessageData
import com.layaa.im.bean.data.ImageMessageData
import com.layaa.im.bean.data.SystemNoticeMessageData
import com.layaa.im.bean.data.TextMessageData
import com.layaa.language.LanguageController
import com.layaa.libui.getString
import com.layaa.libui.utils.DebugUtil
import com.layaa.libui.utils.ImageUtils
import com.layaa.libui.widget.at.ChatAtData
import com.layaa.libutils.GsonUtils
import com.layaa.libutils.NetUtils
import com.layaa.libutils.module_log.LogUtils
import com.layaa.libutils.module_thread.task.BaseMessageLoop
import com.layaa.libutils.module_thread.task.ThreadPool
import com.layaa.libutils.toast.ToastUtils
import com.layaa.roomlogic.R
import com.layaa.roomlogic.constant.RoomCloseType
import com.layaa.roomlogic.constant.RoomManageType
import com.layaa.roomlogic.datastore.BaseRoomDataStore
import com.layaa.roomlogic.datastore.BaseRoomDataStore.Companion.currentRoomId
import com.layaa.roomlogic.datastore.CloseRoomDataStore
import com.layaa.roomlogic.datastore.SeatDataStore
import com.layaa.roomlogic.entity.BaseReplyEntity
import com.layaa.roomlogic.entity.EmojiReplyEntity
import com.layaa.roomlogic.entity.GiftChatMessageData
import com.layaa.roomlogic.entity.GiftInfoBean
import com.layaa.roomlogic.entity.ImageReplyEntity
import com.layaa.roomlogic.entity.RoomChatMessageExtra
import com.layaa.roomlogic.entity.RoomSeatInfo
import com.layaa.roomlogic.entity.RoomUserEntity
import com.layaa.roomlogic.entity.SearchUserEntity
import com.layaa.roomlogic.entity.TextReplyEntity
import com.layaa.roomlogic.entity.UserPropType
import com.layaa.roomlogic.event.ImEnterRoomErrorEvent
import com.layaa.roomlogic.module.annotations.RoomMode
import com.layaa.roomlogic.module.cell.RoomCoroutineScope
import com.layaa.roomlogic.module.event.ReceiveSeatMsgEvent
import com.layaa.roomlogic.module.event.RoomLevelUpEvent
import com.layaa.roomlogic.module.score.ScoreAction
import com.layaa.roomlogic.module.score.ScoreStatusUpdate
import com.layaa.roomlogic.module.view.im.NoticeViewHolder
import com.layaa.roomlogic.util.RoomChatSpanBuilder
import com.layaa.roomlogic.util.RoomConfigManager
import com.layaa.shopapi.shop.ShopRouter
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import org.greenrobot.eventbus.EventBus
import org.json.JSONObject
import java.lang.ref.WeakReference
import java.util.UUID
import java.util.concurrent.CopyOnWriteArrayList
import java.util.concurrent.atomic.AtomicInteger
import kotlin.random.Random

/**
 *<AUTHOR>
 *@date 2023/4/10
 *@des
 **/
class RoomImHelper : EventRoomObserver, EventMsgRevokedObserver, EventMsgModifiedObserver {
    companion object {

        const val TAG = "RoomImHelper"
        private const val JOIN_ROOM_TIMEOUT = 5000L

        const val INVITE_POSITION = -1

        private const val MSG_HEART = 0X1000
        private const val HEART_TIME = 30000L

        private const val MAX_HEARTBEAT_ERROR = 3

        @JvmStatic
        val instance: RoomImHelper by lazy { RoomImHelper() }
    }

    var currentPosition = INVITE_POSITION
    var bottomPosition = INVITE_POSITION

    private var messageList = CopyOnWriteArrayList<MessageTypedEnvelope>()

    private var imCallBack: WeakReference<SendImCallback>? = null

    private val heartbeatQueue = HeartbeatQueue()

    private val errorCount = AtomicInteger(0)

    var heartCode = ImEvent.ChannelMsg.ROOM_HEART

    var cachedSendMsgCellText: CharSequence? = null

    private var roomId: String? = null

    private var heartCallback = object : IMSendCallback {
        override fun onSend(ec: Int, em: String?) {
            LogUtils.i(TAG, "heartCallback, ec: $ec; em: $em")
            if (ec != 0) {
                //发送心跳失败 进行错误次数判断
                if (errorCount.get() > MAX_HEARTBEAT_ERROR) {
                    //错误次数大于3 退房
                    errorCount.set(0)
                    ToastUtils.show(getString(R.string.vr_network_unavailable))
                    EventBus.getDefault().post(ImEnterRoomErrorEvent())
                    return
                }
                errorCount.incrementAndGet()
                return
            }
            errorCount.set(0)
        }
    }

    fun enterRoom(roomId: String, roomMode: Int = RoomMode.MODE_VOICE) {
        heartbeatQueue.Run()
        errorCount.set(0)
        heartbeatQueue.sendEmptyMessageDelayed(MSG_HEART, HEART_TIME)
        if (!networkAvailable()) {
            return
        }
        when (roomMode) {
            RoomMode.MODE_LIVE -> {
                heartCode = ImEvent.ChannelMsg.LIVE_HEART
            }

            RoomMode.MODE_VOICE -> {
                heartCode = ImEvent.ChannelMsg.ROOM_HEART
            }
        }
        this.roomId = roomId
        bottomPosition = INVITE_POSITION
        currentPosition = INVITE_POSITION
        ChatRouter.joinRoom(roomId, JOIN_ROOM_TIMEOUT) { ec, em ->
            if (ec != 0) {
                //进房失败 返回
                EventBus.getDefault().post(ImEnterRoomErrorEvent())
//                ToastUtils.show(LanguageController.getInstance().getString(R.string.net_error))
                if (DebugUtil.debug) {
                    ToastUtils.show("im 进房失败 $ec:$em")
                }
            }
            LogUtils.i(TAG, "ec:$ec;joinRoom:$em")
        }
        ChatRouter.registerRoomEvent(TAG, this)
        ChatRouter.registerMsgRevokedEvent(TAG, this)
    }

    fun quitRoom(roomId: String?) {
        messageList.clear()
        heartbeatQueue.Quit()
        errorCount.set(0)
        cachedSendMsgCellText = null
        bottomPosition = INVITE_POSITION
        currentPosition = INVITE_POSITION
        this.roomId = null
        ChatRouter.quitRoom(roomId, JOIN_ROOM_TIMEOUT) { ec, em ->
            if (ec != 0) {
                //quit fail 需要做处理

            }
            LogUtils.i(TAG, "ec:$ec;quitRoom:$em")
        }
        ChatRouter.unregisterEvent(TAG)
        ChatRouter.unregisterMsgRevokedEvent(TAG)
    }

    fun clearAllMsg() {
        messageList.clear()
    }

    fun addTextToMessageList(content: CharSequence?, eventId: Int) =
        RoomCoroutineScope.launch(Dispatchers.Main) {
            val data = mutableMapOf(
                NoticeViewHolder.CONTENT to (content ?: "")
            )
            val message = MessageTypedEnvelope.SystemNoticeMessageEnvelope(
                UUID.randomUUID().toString(),
                LoginRouter.getUserId(),
                roomId ?: return@launch,
                System.currentTimeMillis(),
                MessageStatus.SEND_SUCCESS,
                ChatType.ROOM,
                -1,
                MessageTypedContent(
                    DataType.SYSTEM_NOTICE,
                    SystemNoticeMessageData(
                        eventId,
                        data
                    ),
                    MessageVisibility()
                )
            )
            messageList.add(message)
            imCallBack?.get()?.onSuccess(message)
        }

    //代发用户公屏消息
    fun addTextToUserMessageList(jsonData: JSONObject) =
        RoomCoroutineScope.launch(Dispatchers.Main) {
            val newMessage =
                MessageTypedEnvelope.TextMessageEnvelope(
                    UUID.randomUUID().toString(),
                    jsonData.optJSONObject("sender").optString("uid"),
                    roomId ?: return@launch,
                    System.currentTimeMillis(),
                    MessageStatus.SEND_SUCCESS,
                    ChatType.ROOM,
                    -1,
                    MessageTypedContent(
                        DataType.TEXT,
                        TextMessageData(
                            jsonData.optJSONObject("msgs")
                                .optString(LanguageController.getLanguage()) ?: ""
                        ),
                        MessageVisibility(),
                        jsonData
                    )
                )
            messageList.add(newMessage)
            imCallBack?.get()?.onSuccess(newMessage)
        }

    fun sendTextMessage(
        text: String?,
        atData: ChatAtData?,
        reply: BaseReplyEntity?
    ) = RoomCoroutineScope.launch(Dispatchers.Main) {
        try {
            if (TextUtils.isEmpty(text)) {
                return@launch
            }

            val extra = RoomChatMessageExtra<BaseReplyEntity>()
            extra.sender = createRoomUserSelf()
            extra.atString = atData
            extra.reply = reply
            extra.replyDataType = when (reply) {
                is ImageReplyEntity -> DataType.IMG.value
                is TextReplyEntity -> DataType.TEXT.value
                is EmojiReplyEntity -> DataType.STICKER.value
                else -> null
            }

            val message = MessageTypedEnvelope.TextMessageEnvelope(
                UUID.randomUUID().toString(),
                LoginRouter.getUserId(),
                roomId ?: return@launch,
                System.currentTimeMillis(),
                MessageStatus.SENDING,
                ChatType.ROOM,
                -1,
                MessageTypedContent(
                    DataType.TEXT,
                    TextMessageData(text ?: ""),
                    MessageVisibility(),
                    JSONObject(GsonUtils.toJson(extra) ?: "{}")
                )
            )

            sendMessage(message)

        } catch (exception: Exception) {
            LogUtils.e(exception)
        }
    }


    private fun sendMessage(
        message: MessageTypedEnvelope,
        isAddSelf: Boolean = true,
        onSuccess: (() -> Unit)? = null
    ) {
        ChatRouter.sendRoomMsg(message) { ec, em ->
            LogUtils.i(TAG, "ec: $ec; sendRoomMsg: $em")
            ThreadPool.runOnUiThread {
                if (ec != 0) {
                    imCallBack?.get()?.onError(ec, em)
                } else {
                    if (!TextUtils.equals(roomId, message.to)) {
                        return@runOnUiThread
                    }
                    // 给自己也发一条
                    if (isAddSelf) {
                        addMessage(message)
                    }
                    onSuccess?.invoke()
                }
            }
        }
    }

    private fun addGiftChatMessage(msg: MessageTypedEnvelope) {
        if (msg !is MessageTypedEnvelope.SystemNoticeMessageEnvelope) {
            return
        }
        val jsonData = JSONObject(msg.content.content.data ?: emptyMap<String, Any>())
        val giftInfo = GsonUtils.fromJson(jsonData.toString(), GiftInfoBean::class.java)
        giftInfo?.receivers?.forEach {
            val content = GiftChatMessageData(
                giftInfo.sender,
                it,
                (giftInfo.sendGiftInfo?.giftAmount ?: 0) *
                        (giftInfo.sendGiftInfo?.repeatCount ?: 0),
                giftInfo.sendGiftInfo?.previewUrl ?: "",
            )
            val subMsg = MessageTypedEnvelope.SystemNoticeMessageEnvelope(
                msg.id,
                msg.from,
                msg.to,
                msg.time,
                msg.status,
                msg.chatType,
                msg.localCustomNumber,
                MessageTypedContent(
                    DataType.SYSTEM_NOTICE,
                    SystemNoticeMessageData(
                        msg.content.content.eventId,
                        GsonUtils.fromJson(
                            GsonUtils.toJson(content),
                            object : TypeToken<Map<String, Any>>() {}.type
                        )
                    ),
                    MessageVisibility()
                )
            )
            addMessage(subMsg)
        }
    }

    fun addMessage(message: MessageTypedEnvelope) {
        messageList.add(message)
        imCallBack?.get()?.onSuccess(message)
    }

    /**
     * 发送图片消息
     */
    fun sendImageMessage(
        image: String,
        atData: ChatAtData?,
    ) = RoomCoroutineScope.launch(Dispatchers.Main) {
        if (!networkAvailable()) {
            return@launch
        }

        val extra = RoomChatMessageExtra<BaseReplyEntity>()
        extra.sender = createRoomUserSelf()
        extra.atString = atData

        val message = MessageTypedEnvelope.ImageMessageEnvelope(
            UUID.randomUUID().toString(),
            LoginRouter.getUserId(),
            roomId ?: return@launch,
            System.currentTimeMillis(),
            MessageStatus.SENDING,
            ChatType.ROOM,
            -1,
            MessageTypedContent(
                DataType.IMG,
                ImageMessageData("", ImageUtils.getImageRatio(image)),
                MessageVisibility(),
                JSONObject(GsonUtils.toJson(extra) ?: "{}")
            )
        )

        // 先展示出来
        addMessage(message)

        try {
            val upload =
                UploadFileRouter.uploadFileToServer(image, UploadFileRouter.ResourceType.IM)
            val imageUrl = Uri.decode(upload.resourceUrl)
            message.content.content.url = imageUrl
            ChatRouter.sendRoomMsg(message) { ec, em ->
                LogUtils.i(TAG, "ec: $ec; sendRoomMsg: $em")
                ThreadPool.runOnUiThread {
                    if (ec != 0) {
                        message.status = MessageStatus.SEND_FAILED
                    } else {
                        message.status = MessageStatus.SEND_SUCCESS
                    }
                    // 更新状态
                    val position = messageList.indexOf(message)
                    if (position > -1) {
                        messageList[position] = message
                    } else {
                        messageList.add(message)
                    }
                    imCallBack?.get()?.onSuccess(message)
                }
            }
        } catch (e: Exception) {
            LogUtils.e(e)
            // 更新状态
            message.status = MessageStatus.SEND_FAILED
            val position = messageList.indexOf(message)
            if (position > -1) {
                messageList[position] = message
            } else {
                messageList.add(message)
            }
            imCallBack?.get()?.onSuccess(message)
        }

    }

    fun sendEmojiMessage(
        emote: EmoteEntity,
        atData: ChatAtData?,
    ) = RoomCoroutineScope.launch(Dispatchers.Main) {

        val extra = RoomChatMessageExtra<BaseReplyEntity>()
        extra.sender = createRoomUserSelf()
        extra.atString = atData

        val message = MessageTypedEnvelope.EmojiMessageEnvelope(
            UUID.randomUUID().toString(),
            LoginRouter.getUserId(),
            roomId ?: return@launch,
            System.currentTimeMillis(),
            MessageStatus.SENDING,
            ChatType.ROOM,
            -1,
            MessageTypedContent(
                DataType.STICKER,
                EmojiMessageData(emote.name, emote.type),
                MessageVisibility(),
                JSONObject(GsonUtils.toJson(extra) ?: "{}")
            )
        )

        sendMessage(message, RoomConfigManager.canShowEmojiInChat(roomId)) {
            RoomEventHelper.postEvent(ReceiveSeatMsgEvent(message))
        }

    }

    fun sendDiceMessage() = RoomCoroutineScope.launch(Dispatchers.Main) {
        if (!networkAvailable()) {
            return@launch
        }

        val extra = RoomChatMessageExtra<BaseReplyEntity>()
        extra.sender = createRoomUserSelf()

        val eventId = ImEvent.CustomRoomMsg.TYPE_ROOM_DICE
        val message = MessageTypedEnvelope.SystemNoticeMessageEnvelope(
            UUID.randomUUID().toString(),
            LoginRouter.getUserId(),
            roomId ?: return@launch,
            System.currentTimeMillis(),
            MessageStatus.SENDING,
            ChatType.ROOM,
            -1,
            MessageTypedContent(
                DataType.SYSTEM_NOTICE,
                SystemNoticeMessageData(
                    eventId,
                    mapOf(Pair("dice", Random.nextInt(1, 6)))
                ),
                MessageVisibility(),
                JSONObject(GsonUtils.toJson(extra) ?: "{}")
            )
        )

        sendMessage(message, RoomConfigManager.canShowEmojiInChat(roomId)) {
            RoomEventHelper.postEvent(ReceiveSeatMsgEvent(message))
        }
    }

    fun sendFingerMessage() = RoomCoroutineScope.launch(Dispatchers.Main) {
        if (!networkAvailable()) {
            return@launch
        }
        val extra = RoomChatMessageExtra<BaseReplyEntity>()
        extra.sender = createRoomUserSelf()

        val eventId = ImEvent.CustomRoomMsg.TYPE_ROOM_FINGER_GAME
        val message = MessageTypedEnvelope.SystemNoticeMessageEnvelope(
            UUID.randomUUID().toString(),
            LoginRouter.getUserId(),
            roomId ?: return@launch,
            System.currentTimeMillis(),
            MessageStatus.SENDING,
            ChatType.ROOM,
            -1,
            MessageTypedContent(
                DataType.SYSTEM_NOTICE,
                SystemNoticeMessageData(
                    eventId,
                    mapOf(Pair("finger", Random.nextInt(1, 3)))
                ),
                MessageVisibility(),
                JSONObject(GsonUtils.toJson(extra) ?: "{}")
            )
        )

        sendMessage(message, RoomConfigManager.canShowEmojiInChat(roomId)) {
            RoomEventHelper.postEvent(ReceiveSeatMsgEvent(message))
        }
    }

    private fun networkAvailable(): Boolean {
        val netWork = NetUtils.isNetworkAvailable();
        if (!netWork) {
            ToastUtils.show(R.string.vr_network_unavailable)
        }
        return netWork
    }

    inner class HeartbeatQueue : BaseMessageLoop(TAG) {
        override fun recvHandleMessage(msg: Message): Boolean {
            return when (msg.what) {
                MSG_HEART -> {
                    heartbeatQueue.sendEmptyMessageDelayed(
                        MSG_HEART,
                        HEART_TIME
                    )
                    sendHeartbeatMessage()
                    true
                }

                else -> {
                    false
                }
            }
        }

    }

    private fun sendHeartbeatMessage() {
        val message = MessageTypedEnvelope.HeartbeatMessageEnvelope(
            UUID.randomUUID().toString(),
            LoginRouter.getUserId(),
            LoginRouter.getUserId(),
            System.currentTimeMillis(),
            MessageStatus.SENDING,
            ChatType.SINGLE,
            -1,
            MessageTypedContent(
                DataType.HEARTBEAT,
                HeartbeatMessageData(
                    LoginRouter.getUserId(),
                    roomId ?: return,
                    ""
                ),
                MessageVisibility()
            )
        )

        ChatRouter.sendChannelMsg(message, heartCallback)
    }

    fun getAllMsgList(): List<MessageTypedEnvelope> {
        return messageList
    }

    fun setImCallback(callback: SendImCallback?) {
        imCallBack?.clear()
        callback?.let {
            imCallBack = WeakReference(it)
        }
    }

    interface SendImCallback {
        fun onSuccess(message: MessageTypedEnvelope)
        fun onDelMsg(message: MessageTypedEnvelope)

        fun onError(errorCode: Int, errorMsg: String)
    }

    override fun onReceiveRoom(message: MessageTypedEnvelope): Boolean {
        if (!TextUtils.equals(message.from, this.roomId)) {
            //接受的消息不是当前房间的 返回
            return false
        }
        when (message) {
            is MessageTypedEnvelope.TextMessageEnvelope -> {
                addMessage(message)
                return false
            }

            is MessageTypedEnvelope.ImageMessageEnvelope -> {
                addMessage(message)
                return false
            }

            is MessageTypedEnvelope.EmojiMessageEnvelope -> {
                if (RoomConfigManager.canShowEmojiInChat(roomId)) {
                    addMessage(message)
                }
                RoomEventHelper.postEvent(ReceiveSeatMsgEvent(message))
                return false
            }

            is MessageTypedEnvelope.SystemNoticeMessageEnvelope -> {
                val eventId = message.content.content.eventId
                val jsonData =
                    JSONObject(message.content.content.data ?: emptyMap<String, String>())
                return when (eventId) {
                    ImEvent.CustomRoomMsg.IM_SYSTEM_USER_TEXT -> {
                        //代发用户消息
                        addTextToUserMessageList(jsonData)
                        false
                    }

                    ImEvent.CustomRoomMsg.IM_SYSTEM_TEXT -> {
                        val content = jsonData.optString(LanguageController.getLanguage())
                        if (content.isNotBlank()) {
                            addTextToMessageList(
                                content,
                                ImEvent.CustomRoomMsg.TYPE_NORMAL_TIPS,
                            )
                        }
                        false
                    }

                    ImEvent.CustomRoomMsg.TYPE_ROOM_FINGER_GAME -> {
                        if (RoomConfigManager.canShowEmojiInChat(roomId)) {
                            addMessage(message)
                        }
                        RoomEventHelper.postEvent(ReceiveSeatMsgEvent(message))
                        false
                    }

                    ImEvent.CustomRoomMsg.TYPE_ROOM_SLOT_MACHINE, ImEvent.CustomRoomMsg.TYPE_ROOM_DICE -> {
                        if (RoomConfigManager.canShowEmojiInChat(roomId)) {
                            addMessage(message)
                        }
                        RoomEventHelper.postEvent(ReceiveSeatMsgEvent(message))
                        false
                    }


                    ImEvent.CustomRoomMsg.USER_LEVEL_UP -> {
                        addMessage(message)
                        val roomUser = jsonData.optJSONObject("userInfo")?.let {
                            GsonUtils.fromJson(it.toString(), SearchUserEntity::class.java)
                        }
                        if (roomUser?.uid == LoginRouter.getUserId()) {
                            LoginRouter.updateLevel(roomUser.userLevelDTO?.level ?: 0)
                            RoomEventHelper.postEvent(RoomLevelUpEvent())
                        }
                        false
                    }

                    ImEvent.CustomRoomMsg.GIFT_TEXT_MESSAGE, ImEvent.CustomRoomMsg.GIFT_MESSAGE -> {
                        addGiftChatMessage(message)
                        false
                    }

                    ImEvent.CustomRoomMsg.TYPE_SYNC_MIC -> {
                        val type = object : TypeToken<List<RoomSeatInfo>>() {
                        }.type
                        val seatInfoDTOList = GsonUtils.fromJson<List<RoomSeatInfo>>(
                            jsonData.optJSONArray("seatInfo")?.toString(), type
                        )
                        SeatDataStore.instance.updateSelfSeatInfo(seatInfoDTOList)
                        false
                    }

                    ImEvent.CustomRoomMsg.TYPE_USER_MANAGER -> {

                        val manageType = jsonData.optInt("manageType")
                        val userInfo = GsonUtils.fromJson(
                            jsonData.optJSONObject("userInfo")?.toString(),
                            RoomUserEntity::class.java
                        )
                        val remoteUserInfo = GsonUtils.fromJson(
                            jsonData.optJSONObject("remoteUserInfo")?.toString(),
                            RoomUserEntity::class.java
                        )
                        val remoteIsSelf =
                            TextUtils.equals(remoteUserInfo?.uid, LoginRouter.getUserId())
                        when (manageType) {
                            RoomManageType.BAN_COMMENT.type -> {
                                addTextToMessageList(
                                    RoomChatSpanBuilder.with(R.string.room_comment_mute_comment)
                                        .baseColor("#FF5470".toColorInt())
                                        .addUid(
                                            remoteUserInfo?.nickName ?: "",
                                            remoteUserInfo?.uid ?: "",
                                            Color.WHITE
                                        )
                                        .build(),
                                    ImEvent.CustomRoomMsg.TYPE_NORMAL_TIPS
                                )
                            }

                            RoomManageType.UNBAN_COMMENT.type -> {

                            }

                            RoomManageType.KICK.type -> {
                                if (remoteIsSelf) {
                                    CloseRoomDataStore.closeRoom(
                                        roomId,
                                        BaseRoomDataStore.QUIT_ROOM_REASON_KICK_OUT,
                                        if (heartCode == LIVE_HEART) {
                                            RoomMode.MODE_LIVE
                                        } else {
                                            RoomMode.MODE_VOICE
                                        }
                                    )
                                    ToastUtils.show(R.string.vr_profile_kicked_notice)
                                } else {
                                    addTextToMessageList(
                                        RoomChatSpanBuilder.with(R.string.room_comment_block)
                                            .baseColor("#FF5470".toColorInt())
                                            .addUid(
                                                remoteUserInfo?.nickName ?: "",
                                                remoteUserInfo?.uid ?: "",
                                                Color.WHITE
                                            )
                                            .build(),
                                        ImEvent.CustomRoomMsg.TYPE_NORMAL_TIPS
                                    )
                                }

                            }

                            RoomManageType.UNBLOCK.type -> {

                            }

                            RoomManageType.ADD_ADMIN.type -> {
                                addTextToMessageList(
                                    RoomChatSpanBuilder.with(R.string.room_comment_add_manage)
                                        .addUid(
                                            remoteUserInfo?.nickName ?: "",
                                            remoteUserInfo?.uid ?: "",
                                            "#FF9E3E".toColorInt()
                                        )
                                        .build(),
                                    ImEvent.CustomRoomMsg.TYPE_NORMAL_TIPS
                                )
                            }

                            RoomManageType.DEL_ADMIN.type -> {

                            }

                            RoomManageType.LOCK_ROOM.type -> {

                            }

                            RoomManageType.UNLOCK_ROOM.type -> {

                            }

                            RoomManageType.CLEAR_SCREEN.type -> {

                            }

                            RoomManageType.CLOSE_COMMENT.type -> {

                            }

                            RoomManageType.OPEN_COMMENT.type -> {

                            }

                            RoomManageType.KICK_SEAT.type -> {
                                if (remoteIsSelf) {
                                    ToastUtils.show(R.string.tips_be_kick_out_mic)
                                }
                            }
                        }
                        false
                    }

                    ImEvent.CustomRoomMsg.TYPE_MIC_MANAGER -> {
                        val seatId = jsonData.optInt("seatId", -1)
                        if (seatId < 0) {
                            return false
                        }
                        // 1 静音 2 解除静音  3 锁定 4 解除锁定
                        val manageType =
                            jsonData.optInt("manageType", -1)

                        SeatDataStore.instance.updateSelfSeatStatus(seatId, manageType)
                        false
                    }

                    ImEvent.CustomRoomMsg.TYPE_ENTER_ROOM -> {
                        addMessage(message)
                        false
                    }

                    ImEvent.CustomRoomMsg.TYPE_ROOM_SCORE_STATUS_UPDATE -> {
                        val update =
                            GsonUtils.fromJson(jsonData.toString(), ScoreStatusUpdate::class.java)
                                ?: return false
                        val notices = arrayListOf<String?>()
                        when (ScoreAction.fromInt(update.action)) {
                            ScoreAction.START -> {
                                notices.add(update.startMsg[LanguageController.getLanguage()])
                            }

                            ScoreAction.END -> {
                                notices.add(update.endMsg[LanguageController.getLanguage()])
                            }

                            ScoreAction.RESTART -> {
                                notices.add(update.endMsg[LanguageController.getLanguage()])
                                notices.add(update.startMsg[LanguageController.getLanguage()])
                            }

                            ScoreAction.OFF -> {
                                notices.add(update.closeMsg[LanguageController.getLanguage()])
                            }
                        }
                        notices.filterNotNull().forEach {
                            addTextToMessageList(
                                it,
                                ImEvent.CustomRoomMsg.TYPE_NORMAL_TIPS,
                            )
                        }
                        false
                    }

                    ImEvent.CustomRoomMsg.ROOM_CLOSE -> {
                        val type = jsonData.optInt("origin", RoomCloseType.ANCHOR.value)
                        when (RoomCloseType.fromInt(type)) {
                            RoomCloseType.ANCHOR, RoomCloseType.BACKEND, RoomCloseType.CLEAN -> {
                                ToastUtils.show(R.string.room_close_normal)
                            }

                            RoomCloseType.BANNED -> {
                                ToastUtils.show(R.string.room_close_banned)
                            }
                        }
                        CloseRoomDataStore.closeRoom(
                            currentRoomId, BaseRoomDataStore.QUIT_ROOM_REASON_OTHER,
                            BaseRoomDataStore.currentRoomMode, callApi = false
                        )
                        false
                    }

                    ImEvent.CustomRoomMsg.ROOM_EXIT -> {
                        val roomId = jsonData.optJSONObject("leaveRoomInfo")?.optString("roomId")
                        if (roomId != currentRoomId) {
                            return false
                        }
                        val remoteUid =
                            jsonData.optJSONObject("leaveRoomInfo")?.optJSONObject("userInfoDto")
                                ?.optString("uid")
                        if (remoteUid != LoginRouter.getUserId()) {
                            return false
                        }
                        val type =
                            jsonData.optJSONObject("leaveRoomInfo")?.optInt("originType") ?: 0
                        if (type == 3) {
                            ToastUtils.show(R.string.vr_profile_kicked_notice)
                        }
                        CloseRoomDataStore.closeRoom(
                            currentRoomId, BaseRoomDataStore.QUIT_ROOM_REASON_IM_ERROR,
                            BaseRoomDataStore.currentRoomMode, callApi = false
                        )
                        false
                    }

                    else -> {
                        false
                    }
                }
            }

            else -> {
                return false
            }
        }

    }

    override fun onRevokedMsg(messageId: String) {
        for (i in 0 until messageList.size) {
            val message = messageList[i]
            if (message.id == messageId && message is MessageTypedEnvelope.ImageMessageEnvelope) {
                message.content.content.url = ""
                imCallBack?.get()?.onSuccess(message)
            }

            val roomChatMessageExtra =
                GsonUtils.fromJson(
                    message.content.clientExt.toString(),
                    RoomChatMessageExtra::class.java
                )
            val replyEntity = roomChatMessageExtra?.reply ?: return
            if (message.id == replyEntity.msgId && replyEntity is ImageReplyEntity) {
                replyEntity.image?.url = ""
                imCallBack?.get()?.onSuccess(message)
            }
        }
    }

    override fun onModified(message: MessageTypedEnvelope) {
        onRevokedMsg(message.id)
    }

    private suspend fun createRoomUserSelf(): RoomUserEntity {
        val user = RoomUserEntity.create(LoginRouter.getUserBean() ?: return RoomUserEntity())
        try {
            val messageBubble = ShopRouter.queryWearProp(UserPropType.MESSAGE_BUBBLE.code)
            user.messageBubble = messageBubble?.resourceContent ?: ""
        } catch (t: Throwable) {
            LogUtils.e(t)
        }
        return user
    }
}