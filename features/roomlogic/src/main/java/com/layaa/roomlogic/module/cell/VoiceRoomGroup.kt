package com.layaa.roomlogic.module.cell

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.viewbinding.ViewBinding
import com.layaa.roomlogic.module.cell.live.LiveBannerCell
import com.layaa.roomlogic.module.cell.live.LiveRankCell

/**
 *<AUTHOR>
 *@date 2021/7/17
 *@des
 **/
class VoiceRoomGroup(private val parentLayout: ViewGroup) :
    RoomCellGroup<ViewBinding>(parentLayout, 1) {

    init {
        var index = 0
        addRoomCell(RoomBackgroundCell(index++))
        addRoomCell(LiveRankCell(index++))
        addRoomCell(ImCell(index++))
        addRoomCell(VoiceAnchorCell(index++))
        addRoomCell(ScoreCell(index++))
        addRoomCell(VoiceEnterRoomCell(index++))
        addRoomCell(LiveBannerCell(index++))
        addRoomCell(RoomMusicPlayingCell(index++))
        addRoomCell(VoiceRoomBottomCell(index++))
        addRoomCell(GiftPanelCell(index++))
        addRoomCell(VoiceRoomTitleCell(index++))
        addRoomCell(RoomSendMsgCell(index++))
        addRoomCell(CloseLiveCell(index++))
        addRoomCell(FloatBannerCell(index++))
        addRoomCell(RocketEntranceCell(index++))
        addRoomCell(GiftPathCell(index++))
        addRoomCell(GiftBannerCell(index++))
        addRoomCell(GiftEffectCell(index++))
        addRoomCell(PKCell(index++))
    }

    override fun getViewBinding(inflater: LayoutInflater): ViewBinding? {
        return null
    }

    override fun handleEvent(event: Any) {

    }
}