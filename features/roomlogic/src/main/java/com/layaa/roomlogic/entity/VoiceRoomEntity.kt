package com.layaa.roomlogic.entity

import androidx.annotation.Keep
import com.layaa.roomapi.entity.RoomThemePropInfo
import com.layaa.roomlogic.module.pk.PKInfo
import com.layaa.roomlogic.module.score.ScoreBoard
import com.layaa.roomlogic.rocket.bean.RocketData

/**
 *<AUTHOR>
 *@date 2024/5/15
 *@des
 **/
@Keep
data class VoiceRoomEntity(
    val roomProfileDTO: RoomProfileEntity?,
    val ownerInfo: VoiceRoomOwnerInfo?,
    val role: Int,
    val seatUserCommonDTOMap: HashMap<String, RoomUserEntity>?,
    val seatPlayerInfoDTOList: ArrayList<RoomSeatInfo>?,
    val restrictedInfo: RoomRestrictedInfo?,
    val relationStatus: Int,
    val onlineUserCount: Int,
    val imTopOnlineListUpdateInfo: RoomTopListEntity,
    var giftValueAmount: Long = 0,
    val scoreboard: ScoreBoard? = null,
    val pk: PKInfo? = null,
    val rocket: RocketData? = null,
    val themeData: Map<String, RoomThemePropInfo> = emptyMap()
)