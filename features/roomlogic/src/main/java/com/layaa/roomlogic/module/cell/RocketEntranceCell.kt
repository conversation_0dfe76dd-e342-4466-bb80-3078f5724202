package com.layaa.roomlogic.module.cell

import android.text.TextUtils
import android.view.LayoutInflater
import android.view.ViewGroup
import android.view.animation.AnimationUtils
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.view.isVisible
import androidx.fragment.app.FragmentActivity
import com.bumptech.glide.Glide
import com.layaa.accountapi.login.LoginRouter
import com.layaa.gift.gifteffect.bean.GiftEffect
import com.layaa.gift.gifteffect.bean.im.GiftSendInfo
import com.layaa.libutils.ClickValidUtil
import com.layaa.libutils.GsonUtils
import com.layaa.roomlogic.util.RocketCountdownHelper
import com.layaa.libutils.dp
import com.layaa.libutils.toast.ToastUtils
import com.layaa.roomlogic.R
import com.layaa.roomlogic.databinding.RoomLayoutRocketEntranceBinding
import com.layaa.roomlogic.entity.GiftInfoBean
import com.layaa.roomlogic.entity.RoomUserEntity
import com.layaa.roomlogic.module.event.ReceiveGiftEvent
import com.layaa.roomlogic.module.helper.RoomEventHelper
import com.layaa.roomlogic.module.view.gift.GiftHelper
import com.layaa.roomlogic.rocket.RocketDialogManager
import com.layaa.roomlogic.rocket.RocketStateManage
import com.layaa.roomlogic.rocket.bean.RocketData
import com.layaa.roomlogic.rocket.dialog.OpenBxDialog
import com.layaa.roomlogic.rocket.dialog.RocketAwardDetailDialog
import com.layaa.roomlogic.rocket.event.RoomRocketEffectEntity
import com.layaa.roomlogic.viewmodel.RoomInfoViewModel
import org.json.JSONObject
import java.util.UUID
import kotlin.uuid.Uuid

class RocketEntranceCell(orderIndex: Int) : RoomCell<RoomLayoutRocketEntranceBinding>(orderIndex) {

    private var lastRoundId: Long? = null//上一回合ID
    private var lastState: Int = 0//上一回合状态
    private var lastProgress: Long = 0////上一回合进度
    private var lastRocketData: RocketData? = null

    private var roomInfoViewModel: RoomInfoViewModel? = null
    private val openCountdown by lazy {
        RocketCountdownHelper(
            intervalMillis = 1000L,
            onTick = { timeStr ->
                viewBinding?.bxCountdownTV?.isVisible = true
                viewBinding?.bxCountdownTV?.text = timeStr
            },
            onFinish = {
                viewBinding?.bxCountdownTV?.isVisible = false
                viewBinding?.bxIv?.clearAnimation()
                updateRocketProgress(0)
            }
        )
    }
    private val waitCountdown by lazy {
        RocketCountdownHelper(
            intervalMillis = 1000L,
            onTick = { timeStr ->
            },
            onFinish = {
                showAvailableBx(10000)
            }
        )
    }

    override fun onCreate() {
        super.onCreate()
        roomInfoViewModel = getViewModel(RoomInfoViewModel::class.java)
        roomInfoViewModel?.roomRocketLiveData?.observe(this) {
            getContext() ?: return@observe
            viewBinding?.rocketIv ?: return@observe
            when {
                //新回合，比上次大 → 重置
                it.roundId > (lastRoundId ?: -1) -> {
                    lastRoundId = it.roundId
                    lastState = 0
                    lastProgress = 0
                    // 关闭上一轮所有开宝箱弹窗
                    RocketDialogManager.dismissAll()
                }

                // 老回合，直接丢弃
                it.roundId < (lastRoundId ?: -1) -> return@observe

                // 同一回合但状态回退 → 丢弃
                it.roundId == lastRoundId && it.state < lastState -> return@observe

                // 同一回合，同状态=待开奖/可抽奖 → 重复丢弃
                it.roundId == lastRoundId && it.state == lastState && it.state > 1 -> return@observe

                // 同一回合，进行中状态但进度没有递增 → 丢弃
                it.roundId == lastRoundId && it.state == 1 && it.currentContribution <= lastProgress -> return@observe
            }
            // 更新本地记录
            lastRoundId = it.roundId
            lastState = it.state
            lastProgress = it.currentContribution
            lastRocketData = it

            //根据开关配置 显示/隐藏 入口
            viewBinding?.root?.isVisible = it.enabled

            when (it.state) {
                RocketStateManage.STATE_IN_PROGRESS -> {
                    //火箭爆炸进行中
                    if (!TextUtils.equals(
                            viewBinding?.rocketIv?.tag?.toString(),
                            it.maxLevel.toString()
                        )
                    ) {
                        viewBinding?.rocketIv?.tag = it.maxLevel
                        Glide.with(getContext()!!).load(it.rocketIconUrl)
                            .into(viewBinding?.rocketIv!!)
                    }
                    var curProgress =
                        it.currentContribution.toDouble().div(it.requiredContribution).times(100)
                            .toInt()
                    if (curProgress >= 100) {
                        curProgress = 100
                    }
                    updateRocketProgress(curProgress)
                }

                RocketStateManage.STATE_EFFECTIVE_WAIT_REWARD -> {
                    //火箭爆炸动效，等待抢宝箱
                    if (!TextUtils.equals(
                            viewBinding?.bxIv?.tag?.toString(),
                            it.maxLevel.toString()
                        )
                    ) {
                        viewBinding?.bxIv?.tag = it.maxLevel
                        Glide.with(getContext()!!).load(it.treasureBoxIconUrl)
                            .into(viewBinding?.bxIv!!)
                    }
                    RoomEventHelper.postEvent(
                        RoomRocketEffectEntity(
                            it.effectUrl,
                            it.top1user?.photo ?: ""
                        )
                    )
                    showUnavailableBx()
                    RoomEventHelper.postEvent(
                        ReceiveGiftEvent().msg(
                            GiftHelper.simulationSendGiftData(
                                it.top1user?.nickName ?: "",
                                it.top1user?.uid ?: "",
                                it.top1user?.photo ?: "",
                                it.effectUrl
                            )
                        )
                    )
                }

                RocketStateManage.STATE_EFFECTIVE_OPEN_REWARD -> {
                    //可抢开宝箱动效
                    showAvailableBx(it.duration)
                }
            }
        }
    }

    override fun onViewCreate() {
        super.onViewCreate()
        viewBinding?.bxIv?.setOnClickListener {
            if (viewBinding?.bxCountdownTV?.isVisible == false) {
                ToastUtils.show("还未到开抢时间，请稍后")
                return@setOnClickListener
            }
//            (getContext() as? FragmentActivity)?.let {
//                OpenBxDialog.start(it.supportFragmentManager,roomInfoViewModel?.roomRocketLiveData)
//            }
        }
        viewBinding?.rocketView?.setOnClickListener {
            if (!ClickValidUtil.clickValidShort()) {
                return@setOnClickListener
            }
            lastRocketData?.let { data ->
                (getContext() as? FragmentActivity)?.let {
                    RocketAwardDetailDialog.start(
                        it.supportFragmentManager,
                        roomInfoViewModel?.getRoomId() ?: "",
                        data
                    )
                }
            }

        }

        updateRocketProgress(0)
    }

    override fun generateLayoutParams(roomMode: Int, roomType: String?): ViewGroup.LayoutParams {
        val params = ConstraintLayout.LayoutParams(
            ViewGroup.LayoutParams.WRAP_CONTENT,
            ViewGroup.LayoutParams.WRAP_CONTENT
        )
        params.bottomToTop = R.id.bottom_view
        params.endToEnd = ConstraintLayout.LayoutParams.PARENT_ID
        params.bottomMargin = 12.dp
        params.marginEnd = 12.dp
        return params
    }

    override fun changeRoomMode(roomMode: Int, roomType: String?) {
        addSelf(roomMode, roomType)
    }

    override fun getViewBinding(inflater: LayoutInflater): RoomLayoutRocketEntranceBinding? {
        return RoomLayoutRocketEntranceBinding.inflate(inflater)
    }

    override fun handleEvent(event: Any) {

    }

    //展示可抢态宝箱
    fun showAvailableBx(totalTime: Long) {
        viewBinding?.rocketView?.isVisible = false
        viewBinding?.openBxView?.isVisible = true
        viewBinding?.bxCountdownTV?.isVisible = false
        openCountdown.startSeconds(totalTime)
        viewBinding?.bxIv?.startAnimation(
            AnimationUtils.loadAnimation(getContext(), R.anim.chest_shake)
        )
        (getContext() as? FragmentActivity)?.let {
            roomInfoViewModel?.roomRocketLiveData?.let { data ->
                data.value?.let { rocketData ->
                    OpenBxDialog.start(
                        it.supportFragmentManager,
                        roomInfoViewModel?.getRoomId().toString(), rocketData
                    )
                }

            }
        }
    }

    //展示静态宝箱
    fun showUnavailableBx() {
        viewBinding?.rocketView?.isVisible = false
        viewBinding?.openBxView?.isVisible = true
        viewBinding?.bxCountdownTV?.isVisible = false
        viewBinding?.bxIv?.clearAnimation()
        // waitCountdown.start(3000L)
    }

    fun updateRocketProgress(curProgress: Int) {
        viewBinding?.rocketView?.isVisible = true
        viewBinding?.openBxView?.isVisible = false
        viewBinding?.rocketProgressBar?.progress = curProgress

    }

    override fun onDestroy() {
        super.onDestroy()
        openCountdown.cancel()
        viewBinding?.bxIv?.clearAnimation()
    }
}