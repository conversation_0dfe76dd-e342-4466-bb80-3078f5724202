package com.layaa.roomlogic.voiceroom.entity

import androidx.annotation.Keep

/**
 *<AUTHOR>
 *@date 2024/4/27
 *@des
 **/
@Keep
data class MicModeEntity(val id: Int, val resId: Int)

enum class MicType(val count: Int) {

    TWO(2),
    FIVE(5),
    EIGHT(8),
    NINE(9),
    TWELVE(12),
    FIFTEEN(15),
    TWENTY(20),
    THIRTY(30);

    companion object {
        fun fromInt(count: Int?): MicType {
            return entries.find { it.count == count } ?: EIGHT
        }
    }
}