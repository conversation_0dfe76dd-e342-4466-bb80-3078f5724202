package com.layaa.roomlogic.module.cell

import android.annotation.SuppressLint
import android.text.TextUtils
import android.view.GestureDetector
import android.view.LayoutInflater
import android.view.MotionEvent
import android.view.View
import android.view.View.OnTouchListener
import android.view.ViewGroup
import android.widget.FrameLayout
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.recyclerview.widget.RecyclerView
import com.layaa.accountapi.login.LoginRouter
import com.layaa.chatapi.ChatRouter
import com.layaa.im.bean.MessageTypedEnvelope
import com.layaa.language.LanguageController
import com.layaa.libui.widget.at.AtUser
import com.layaa.libutils.ClickValidUtil
import com.layaa.libutils.GsonUtils
import com.layaa.libutils.KeyBoardUtil
import com.layaa.libutils.StatusBarUtil
import com.layaa.libutils.UIUtils
import com.layaa.libutils.dp
import com.layaa.roomapi.RoomConst
import com.layaa.roomlogic.R
import com.layaa.roomlogic.databinding.RoomLayoutRoomImBinding
import com.layaa.roomlogic.dialog.RoomChatOptionPopupWindow
import com.layaa.roomlogic.entity.BaseChatMessageExtra
import com.layaa.roomlogic.entity.EmojiReplyEntity
import com.layaa.roomlogic.entity.ImageReplyEntity
import com.layaa.roomlogic.entity.RoomChatMessageExtra
import com.layaa.roomlogic.entity.TextReplyEntity
import com.layaa.roomlogic.event.AtUserEvent
import com.layaa.roomlogic.module.annotations.RoomMode
import com.layaa.roomlogic.module.event.ChatMessageUpdateEvent
import com.layaa.roomlogic.module.event.ClearAllMsgEvent
import com.layaa.roomlogic.module.helper.RoomImHelper
import com.layaa.roomlogic.module.view.im.ChatRoomAdapter
import com.layaa.roomlogic.viewmodel.RoomInfoViewModel
import com.layaa.roomlogic.widget.ChatGestureDetectorListener
import com.layaa.widget.LinearLayoutManagerWrapper
import org.greenrobot.eventbus.EventBus

/**
 *<AUTHOR>
 *@date 2021/7/17
 *@des
 **/
class ImCell(order: Int) : RoomCell<RoomLayoutRoomImBinding>(order), RoomImHelper.SendImCallback {

    companion object {
        var imCellWidth = 0
        var imCellHeight = 0
    }

    private var roomInfoViewModel: RoomInfoViewModel? = null

    private var chatRoomAdapter: ChatRoomAdapter? = null
    private var layoutManager: LinearLayoutManagerWrapper? = null
    private var onClickHeaderListener: (View.() -> Unit)? = null
    private val optionPopupWindow: RoomChatOptionPopupWindow by lazy {
        RoomChatOptionPopupWindow(getContext()!!).apply {
            replyCallback = callback@{
                val roomChatMessageExtra =
                    GsonUtils.fromJson(
                        this.content.clientExt.toString(),
                        BaseChatMessageExtra::class.java
                    ) ?: return@callback

                val replyEntity = when (this) {
                    is MessageTypedEnvelope.TextMessageEnvelope -> {
                        TextReplyEntity(this.content.content.text)
                    }

                    is MessageTypedEnvelope.ImageMessageEnvelope -> {
                        ImageReplyEntity(this.content.content)
                    }

                    is MessageTypedEnvelope.EmojiMessageEnvelope -> {
                        EmojiReplyEntity(this.content.content)
                    }

                    else -> {
                        return@callback
                    }
                }
                replyEntity.msgId = this.id
                replyEntity.sender = roomChatMessageExtra.sender

                val atUser = AtUser(roomChatMessageExtra.sender?.uid ?: "", roomChatMessageExtra.sender?.nickName ?: "")

                getCell<RoomSendMsgCell>(RoomSendMsgCell::class.java.simpleName)?.showReply(
                    replyEntity, AtUserEvent(atUser)
                )
            }
            remindCallback = callback@{
                val roomChatMessageExtra =
                    GsonUtils.fromJson(
                        this.content.clientExt.toString(),
                        BaseChatMessageExtra::class.java
                    ) ?: return@callback
                val atUser = AtUser(roomChatMessageExtra.sender?.uid ?: "", roomChatMessageExtra.sender?.nickName ?: "")
                EventBus.getDefault().post(AtUserEvent(atUser))
            }
        }
    }
    private var atPosition = 0
    private var atPositionList = ArrayList<Int>()//艾特自己消息数组
    private var isHandScroll = false //默认非手动滑动公屏消息

    private val onGestureListener = object : ChatGestureDetectorListener() {
        override fun onListItemClick() {
            getActivity()?.let {
                KeyBoardUtil.hideSoftKeyboardNotAlways(it)
            }
            hideKeyboard()
        }
    }
    private var gestureDetector: GestureDetector = GestureDetector(getContext(), onGestureListener)

    fun refreshList() {
        refreshMsg(RoomImHelper.instance.getAllMsgList())
    }

    override fun onCreate() {
        super.onCreate()
        roomInfoViewModel = getViewModel(RoomInfoViewModel::class.java)
    }

    @SuppressLint("ClickableViewAccessibility")
    override fun onViewCreate() {
        viewBinding?.root?.post {
            ImCell.imCellWidth = viewBinding?.roomImList?.width?.times(0.8)?.toInt() ?: 219
            ImCell.imCellHeight = viewBinding?.roomImList?.height ?: 100
        }

        layoutManager = LinearLayoutManagerWrapper(getActivity())
        (layoutManager as LinearLayoutManagerWrapper).recycleChildrenOnDetach = true
        chatRoomAdapter = ChatRoomAdapter(getContext()!!)
        viewBinding?.imList?.overScrollMode = View.OVER_SCROLL_NEVER
        viewBinding?.imList?.itemAnimator = null

        chatRoomAdapter?.setOnClickHeaderListener {
            onClickHeaderListener?.invoke(this)
        }
        chatRoomAdapter?.onLongClick = { contentView ->
            optionPopupWindow.show(this, contentView)
        }
        chatRoomAdapter?.onImgClick = {
            ChatRouter.gotoPreviewImage(this)
        }
        chatRoomAdapter?.getSelfRoomRole = {
            roomInfoViewModel?.roomRoleData?.value ?: RoomConst.NORMAL_USER
        }
        viewBinding?.imList?.layoutManager = layoutManager
        viewBinding?.imList?.adapter = chatRoomAdapter
        viewBinding?.imList?.addOnScrollListener(object : RecyclerView.OnScrollListener() {
            override fun onScrolled(recyclerView: RecyclerView, dx: Int, dy: Int) {
                super.onScrolled(recyclerView, dx, dy)
                if (!isHandScroll) {
                    //非手动滑动
                    return
                }
                recyclerView.findChildViewUnder(0F, recyclerView.height.toFloat())?.let {
                    RoomImHelper.instance.currentPosition =
                        recyclerView.findContainingViewHolder(it)?.adapterPosition
                            ?: 0

                    if (RoomImHelper.instance.currentPosition >= RoomImHelper.instance.bottomPosition && viewBinding?.unread?.visibility == FrameLayout.VISIBLE) {
                        //viewBinding?.unread?.visibility = FrameLayout.GONE
                        scrollAndRemoveAtPosition()
                    }
                    if (RoomImHelper.instance.currentPosition > RoomImHelper.instance.bottomPosition) {
                        if (RoomImHelper.instance.bottomPosition < getScrollPosition()) {
                            RoomImHelper.instance.bottomPosition++
                            return
                        }
                        RoomImHelper.instance.bottomPosition = getScrollPosition()
                    }
                }
            }

            override fun onScrollStateChanged(recyclerView: RecyclerView, newState: Int) {
                super.onScrollStateChanged(recyclerView, newState)
            }
        })

        viewBinding?.imList?.setOnTouchListener(OnTouchListener { p0, p1 ->
            isHandScroll = true
            return@OnTouchListener gestureDetector.onTouchEvent(p1)
        })

        viewBinding?.root?.setOnClickListener {
            getActivity()?.let {
                KeyBoardUtil.hideSoftKeyboardNotAlways(it)
            }
            hideKeyboard()
        }

        viewBinding?.unread?.setOnClickListener {
            if (!ClickValidUtil.clickValiShortest()) {
                return@setOnClickListener
            }
            goAndRemoveAtPosition()

        }

        viewBinding?.imList?.setOnTouchListener(
            object : OnTouchListener {
                override fun onTouch(v: View?, event: MotionEvent): Boolean {
                    if (v is RecyclerView) {
                        isHandScroll = true
                        return gestureDetector.onTouchEvent(event)
                    }
                    return false
                }

            })
    }

    override fun changeRoomMode(roomMode: Int, roomType: String?) {
        addSelf(roomMode, roomType)
    }

    override fun onAttachToWindow() {
        super.onAttachToWindow()
        refreshList()
        RoomImHelper.instance.setImCallback(this)
    }

    override fun onDetachFromWindow() {
        super.onDetachFromWindow()
        RoomImHelper.instance.setImCallback(null)
        if (viewBinding?.unread?.visibility == FrameLayout.GONE) {
            RoomImHelper.instance.bottomPosition = RoomImHelper.INVITE_POSITION
            RoomImHelper.instance.currentPosition = RoomImHelper.INVITE_POSITION
        }
        optionPopupWindow?.dismiss()
    }


    override fun handleEvent(event: Any) {
        when (event) {
            is ClearAllMsgEvent -> {
                clearMsg(RoomImHelper.instance.getAllMsgList())
            }

            is ChatMessageUpdateEvent -> {
                updateMessage(event.messageId, event.payload)
            }
        }
    }

    private fun updateMessage(messageId: String, payload: Any?) {
        chatRoomAdapter?.update(messageId, payload)
    }

    fun showKeyboard() {
        val layoutParams =
            (viewBinding?.root?.layoutParams as? ConstraintLayout.LayoutParams)
                ?: ConstraintLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, 0)
        layoutParams.topToTop = ConstraintLayout.LayoutParams.PARENT_ID
        layoutParams.topToBottom = ConstraintLayout.LayoutParams.UNSET
        layoutParams.bottomToTop = R.id.roomSendMsg
        layoutParams.topMargin = 0
        layoutParams.height = 0
        getContext()?.let {
            viewBinding?.root?.setBackgroundResource(R.drawable.bg_room_top_chat)
        }

        viewBinding?.root?.setPadding(
            0,
            StatusBarUtil.getStatusBarHeight(getContext()) + UIUtils.getPixels(70F),
            0,
            0
        )
        getCell<PKCell>(PKCell::class.java.simpleName)?.onIMCellFullScreen(true)
    }

    fun hideKeyboard() {
        viewBinding?.root?.setPadding(0, 0, 0, 0)
        viewBinding?.root?.background = null
        viewBinding?.root?.layoutParams = generateLayoutParams(roomMode, roomType)
        getCell<RoomSendMsgCell>(RoomSendMsgCell::class.java.simpleName)?.closeSendView()
        getCell<PKCell>(PKCell::class.java.simpleName)?.onIMCellFullScreen(false)
    }

    override fun generateLayoutParams(
        @RoomMode roomMode: Int,
        roomType: String?
    ): ViewGroup.LayoutParams {
        val layoutParams = ConstraintLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, 0)
        if (roomMode == RoomMode.MODE_VOICE) {
            layoutParams.topToTop = ConstraintLayout.LayoutParams.UNSET
            layoutParams.topToBottom = R.id.room_pk
            layoutParams.bottomToTop = R.id.bottom_view
            layoutParams.topMargin = (-10).dp
            layoutParams.goneTopMargin = 0
        } else {
            layoutParams.bottomToTop = R.id.bottom_view
            layoutParams.height = UIUtils.getPixels(300F)
        }
        return layoutParams
    }

    override fun getViewBinding(inflater: LayoutInflater): RoomLayoutRoomImBinding {
        return RoomLayoutRoomImBinding.inflate(inflater)
    }

    override fun onSuccess(message: MessageTypedEnvelope) {
        showOrUpdateMessage(message)
    }

    override fun onDelMsg(message: MessageTypedEnvelope) {
        chatRoomAdapter?.remove(message)
    }

    override fun onError(errorCode: Int, errorMsg: String) {

    }

    fun showOrUpdateMessage(message: MessageTypedEnvelope) {
        if (TextUtils.equals(message.from, LoginRouter.getUserId())) {
            viewBinding?.unread?.visibility = FrameLayout.GONE
            chatRoomAdapter?.add(message)
            scrollToBottom()
            return
        }
        showNewCount(1)
        chatRoomAdapter?.add(message)
        if (viewBinding?.unread?.visibility == FrameLayout.GONE) {
            scrollToBottom()
        } else {
            val messageExtra = GsonUtils.fromJson(
                message.content.clientExt.toString(),
                RoomChatMessageExtra::class.java
            )
            messageExtra?.atString?.list?.forEach {
                if (TextUtils.equals(LoginRouter.getUserId(), it.uid)) {
                    //at自己的消息 更新下文案
                    atPosition = (chatRoomAdapter?.itemCount ?: 0) - 1
                    atPositionList.add(atPosition)
                }
            }
            showAtUnreadTxt()
        }
    }

    /**
     *是否展示 有人艾特你
     */
    private fun showAtUnreadTxt() {
        if (atPositionList.isNotEmpty()) {
            viewBinding?.unread?.setText(R.string.at_you)
        }
    }

    /**
     * 跳到第一个艾特消息处并删除当前的位置
     */
    private fun goAndRemoveAtPosition() {
        if (atPositionList.isNotEmpty()) {
            scrollToPosition(atPositionList[0])
            atPositionList.removeAt(0)
            if (atPositionList.isEmpty()) {
                showNewCount(-1)
            }
            return
        }
        scrollToBottom()
        viewBinding?.unread?.visibility = FrameLayout.GONE
    }

    /**
     * 滑动第一个艾特消息处并删除当前的位置
     */
    private fun scrollAndRemoveAtPosition() {
        if (atPositionList.size > 0) {
            if (RoomImHelper.instance.currentPosition == atPositionList[0]) {
                atPositionList.removeAt(0)
            }
        }
        if (atPositionList.size == 0) {
            showNewCount(-1)
        }

    }

    private fun showNewCount(newMsgCount: Int) {
        if (RoomImHelper.instance.currentPosition != getScrollPosition()) {
            when (newMsgCount) {
                -1 -> {
                    val count = getScrollPosition() - RoomImHelper.instance.bottomPosition
                    if (count == 0) {
                        viewBinding?.unread?.visibility = FrameLayout.GONE
                        return
                    }
                    viewBinding?.unread?.text = String.format(
                        LanguageController.getInstance().getString(R.string.new_msg),
                        if (count > 99) {
                            "99+"
                        } else {
                            count.toString()
                        }
                    )
                    viewBinding?.unread?.visibility = FrameLayout.VISIBLE
                }

                else -> {
                    val count =
                        getScrollPosition() - RoomImHelper.instance.bottomPosition + newMsgCount
                    viewBinding?.unread?.text = String.format(
                        LanguageController.getInstance().getString(R.string.new_msg),
                        if (count > 99) {
                            "99+"
                        } else {
                            count.toString()
                        }
                    )
                    viewBinding?.unread?.visibility = FrameLayout.VISIBLE
                }
            }

        } else {
            viewBinding?.unread?.visibility = FrameLayout.GONE
        }
    }

    private fun clearMsg(list: List<MessageTypedEnvelope>) {
        viewBinding?.unread?.visibility = FrameLayout.GONE
        chatRoomAdapter?.refreshList(list)
        RoomImHelper.instance.bottomPosition = getScrollPosition()
        RoomImHelper.instance.currentPosition = getScrollPosition()
        scrollToLastBottom(10)
    }

    fun refreshMsg(list: List<MessageTypedEnvelope>) {
        viewBinding?.unread?.visibility = FrameLayout.GONE
        isHandScroll = false
        chatRoomAdapter?.refreshList(list)
        if (RoomImHelper.instance.bottomPosition == RoomImHelper.INVITE_POSITION) {
            RoomImHelper.instance.bottomPosition = getScrollPosition()
            RoomImHelper.instance.currentPosition = RoomImHelper.instance.bottomPosition
        }
        showNewCount(0)
        scrollToLastBottom(300)
    }

    private var scrollTask: ScrollTask? = null

    private fun scrollToPosition(position: Int) {
        if (position < 0) {
            return
        }
        RoomImHelper.instance.currentPosition = position
        RoomImHelper.instance.bottomPosition = RoomImHelper.instance.currentPosition;
        var task: ScrollTask? = scrollTask
        if (task == null) {
            task = ScrollTask()
        } else {
            viewBinding?.imList?.removeCallbacks(task)
        }
        viewBinding?.imList?.postDelayed(task, 30)
        scrollTask = task
    }

    private fun scrollToBottom() {
        atPositionList.clear()
        scrollToBottom(30)
    }

    private fun scrollToBottom(delayMillis: Long) {
        RoomImHelper.instance.currentPosition = getScrollPosition()
        RoomImHelper.instance.bottomPosition = RoomImHelper.instance.currentPosition;
        var task: ScrollTask? = scrollTask
        if (task == null) {
            task = ScrollTask()
        } else {
            viewBinding?.imList?.removeCallbacks(task)
        }
        viewBinding?.imList?.postDelayed(task, delayMillis)
        scrollTask = task
    }

    private fun scrollToLastBottom(delayMillis: Long) {
        if (RoomImHelper.instance.bottomPosition == RoomImHelper.INVITE_POSITION) {
            scrollToBottom(delayMillis)
        } else {
            var task: ScrollTask? = scrollTask
            if (task == null) {
                task = ScrollTask()
            } else {
                viewBinding?.imList?.removeCallbacks(task)
            }
            viewBinding?.imList?.postDelayed(task, delayMillis)
            scrollTask = task
        }
    }


    private fun getScrollPosition(): Int {

        return (chatRoomAdapter?.itemCount ?: 0) - 1
    }

    inner class ScrollTask : Runnable {
        override fun run() {
            viewBinding?.imList?.scrollToPosition(RoomImHelper.instance.currentPosition)
        }
    }
}