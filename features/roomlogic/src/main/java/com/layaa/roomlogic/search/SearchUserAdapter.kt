package com.layaa.roomlogic.search

import android.animation.ValueAnimator
import android.content.Context
import android.os.Bundle
import android.text.TextUtils
import android.view.View
import android.widget.ImageView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.view.isInvisible
import androidx.core.view.isVisible
import com.airbnb.lottie.LottieAnimationView
import com.bumptech.glide.Glide
import com.bumptech.glide.load.resource.bitmap.RoundedCorners
import com.layaa.accountapi.login.LoginConst
import com.layaa.accountapi.login.LoginRouter
import com.layaa.accountapi.profile.ProfileConst
import com.layaa.accountapi.profile.ProfileRouter
import com.layaa.libui.widget.GenderAgeView
import com.layaa.libui.widget.PrettyUidView
import com.layaa.libui.widget.UserLevelView
import com.layaa.libui.widget.nickname.NicknameTextView
import com.layaa.libutils.ClickValidUtil
import com.layaa.libutils.dp
import com.layaa.roomapi.RoomConst
import com.layaa.roomapi.router.RoomRouter
import com.layaa.roomlogic.R
import com.layaa.roomlogic.entity.SearchUserEntity
import com.layaa.roomlogic.module.view.RippleCircleLayout
import com.layaa.roomlogic.util.TextSpannableUtil
import com.layaa.widget.basepage.BaseAdapter
import com.layaa.widget.basepage.BaseViewHolder

/**
 *<AUTHOR>
 *@date 2021/10/20
 *@des
 **/
class SearchUserAdapter(context: Context) : BaseAdapter<SearchUserEntity>(context) {

    var flowClick: (View?.(position: Int) -> Unit)? = null
    override fun getLayoutResId(viewType: Int): Int {
        return R.layout.room_item_search_user
    }

    override fun getViewHolder(viewType: Int, view: View): BaseViewHolder<*> {
        return ViewHolder(view)
    }

    inner class ViewHolder(view: View) : BaseViewHolder<SearchUserEntity>(view) {

        private val ripple: RippleCircleLayout = findViewById(R.id.ripple)
        private val lottieView: ConstraintLayout = findViewById(R.id.lottieView)
        private val lottie: LottieAnimationView = findViewById(R.id.lottie)
        private val photo: ImageView = findViewById(R.id.photo)
        private val flowIv: ImageView = findViewById(R.id.flowIv)

        private val countryFlag: ImageView = findViewById(R.id.countryFlag)
        private val nickname: NicknameTextView = findViewById(R.id.nickname)
        private val uid: PrettyUidView = findViewById(R.id.uid)
        private val gender: GenderAgeView = findViewById(R.id.gender)
        private val userLevelView: UserLevelView = findViewById(R.id.userLevelView)

        init {
            lottie.setAnimation("lottie/room_list_online.json")

            lottie.repeatCount = ValueAnimator.INFINITE
            itemView.setOnClickListener {
                if (!ClickValidUtil.clickValidShort()) {
                    return@setOnClickListener
                }
                val data = it.tag as? SearchUserEntity?
                data ?: return@setOnClickListener
                val bundle = Bundle()
                if (TextUtils.equals(data.uid, LoginRouter.getUserId())) {
                    //自己 到主态
                    bundle.putString("PROFILE_SOURCE", "room");
                    LoginRouter.gotoProfileActivity(bundle)
                    return@setOnClickListener
                }
                bundle.putString(LoginConst.UID, data.uid)
                LoginRouter.gotoOtherProfile(bundle)
            }
            ripple.setOnClickListener {
                if (!ClickValidUtil.clickValidShort()) {
                    return@setOnClickListener
                }
                val data = it.tag as? SearchUserEntity?
                data ?: return@setOnClickListener
                if (TextUtils.isEmpty(data.inRoomId)) {
                    val bundle = Bundle()
                    bundle.putString("PROFILE_SOURCE", "room")
                    bundle.putString(LoginConst.UID, data.uid)
                    LoginRouter.gotoProfile(bundle)
                    return@setOnClickListener
                }
                val bundle = Bundle()
                bundle.putString(RoomConst.ROOM_ID, data.inRoomId)
                bundle.putString(RoomConst.ROOM_SOURCE, "8")
                RoomRouter.gotoVoice(bundle)
            }
            flowIv.setOnClickListener {
                flowClick?.invoke(it, absoluteAdapterPosition)
            }
        }

        override fun update(bean: SearchUserEntity) {
            itemView.tag = bean
            flowIv.tag = bean
            ripple.tag = bean

            gender.show(bean.gender ?: "M", bean.age)
            val searchName = (getContext() as SearchCallback).searchKey
            if (TextSpannableUtil.getStart(bean.nickName, searchName) > -1) {
                TextSpannableUtil.setPartTextColor(
                    nickname,
                    bean.nickName,
                    searchName,
                    "#1FB58D"
                )
            } else {
                nickname.show(bean.nicknameBean)
            }
            uid.setAutoUid(
                TextSpannableUtil.getPartTextColor(
                    "ID:${bean.uid}",
                    (getContext() as SearchCallback).searchKey,
                    "#1FB58D"
                ), bean.prettyUid
            )

            //age.setSelected(true);
            ProfileRouter.loadCountryFlag(countryFlag, bean.country)

            Glide.with(photo)
                .load(bean.photo)
                .transform(RoundedCorners(42.dp))
                .into(photo)
            userLevelView.setUserLevel(bean.userLevelDTO?.level ?: 0)
            flowIv.isInvisible = TextUtils.equals(bean.uid, LoginRouter.getUserId())
            when (bean.relationType) {
                ProfileConst.TYPE_FRIEND -> {//好友
                    flowIv.setImageResource(R.drawable.icon_all_flow)
                }

                ProfileConst.TYPE_FOLLOW -> {//只关注了对方
                    flowIv.setImageResource(R.drawable.icon_single_flow)
                }

                else -> {
                    flowIv.setImageResource(R.drawable.icon_un_flow)
                }
            }
            ripple.setRippleRgbColor(1, 24, 173, 134)
            if (TextUtils.isEmpty(bean.inRoomId)) {
                ripple.setRippleRgbColorString("#ffffff")
                ripple.stopAnim()
                lottie.cancelAnimation()
                lottieView.isVisible = false
            } else {
                ripple.setRippleRgbColorString("#1FB58D")
                ripple.startRippleAnimationForever()
                lottie.playAnimation()
                lottieView.isVisible = true
            }

        }

    }
}