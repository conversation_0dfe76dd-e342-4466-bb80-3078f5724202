package com.layaa.roomlogic.module.cell

import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.appcompat.app.AppCompatActivity
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.view.isVisible
import com.layaa.language.LanguageController
import com.layaa.libui.dialog.CommonTipDialog
import com.layaa.libui.utils.TypefaceHelper
import com.layaa.libutils.dp
import com.layaa.roomlogic.R
import com.layaa.roomlogic.databinding.ScoreLayoutCellBinding
import com.layaa.roomlogic.module.score.ScoreAction
import com.layaa.roomlogic.module.score.ScoreDetailDialog
import com.layaa.roomlogic.module.score.ScoreDuration
import com.layaa.roomlogic.module.score.ScoreResultDialog
import com.layaa.roomlogic.module.score.ScoreStatusUpdate
import com.layaa.roomlogic.module.score.ScoreViewModel
import com.layaa.roomlogic.viewmodel.RoomInfoViewModel
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.cancel
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

/**
 * Created by Todd on 2024/10/18
 */
class ScoreCell(orderIndex: Int) : RoomCell<ScoreLayoutCellBinding>(orderIndex) {

    private var scoreViewModel: ScoreViewModel? = null
    private var roomInfoViewModel: RoomInfoViewModel? = null
    private val countdownScope = CoroutineScope(Dispatchers.Main)
    private var countdownJob: Job? = null

    private val confirmDialog: CommonTipDialog? by lazy {
        getActivity()?.let {
            CommonTipDialog(it)
        } ?: run {
            null
        }
    }

    override fun onCreate() {
        super.onCreate()
        scoreViewModel = getViewModel(ScoreViewModel::class.java)
        roomInfoViewModel = getViewModel(RoomInfoViewModel::class.java)
        scoreViewModel?.scoreStatusData?.observe(this) {
            val canManage =
                roomInfoViewModel?.isRoomOwn() == true || roomInfoViewModel?.isRoomManager() == true
            countdownJob?.cancel()
            when (ScoreAction.fromInt(it.action)) {
                ScoreAction.START, ScoreAction.RESTART -> {
                    mView?.isVisible = true
                    if (it.duration == ScoreDuration.NO_LIMIT.minute) {
                        viewBinding?.txtStatus?.text =
                            LanguageController.getInstance()
                                .getString(R.string.vr_scoreboard_scoring)
                        viewBinding?.lottieScoring?.isVisible = true
                        viewBinding?.lottieScoring?.playAnimation()
                        viewBinding?.imgTimer?.isVisible = false
                    } else {
                        startCountdown(it.startTime, it.duration)
                        viewBinding?.lottieScoring?.isVisible = false
                        viewBinding?.lottieScoring?.cancelAnimation()
                        viewBinding?.imgTimer?.isVisible = true
                    }
                    if (canManage) {
                        viewBinding?.imgEnd?.isVisible = true
                        viewBinding?.txtStatus?.typeface = TypefaceHelper.getBold()
                    } else {
                        viewBinding?.imgEnd?.isVisible = false
                        viewBinding?.txtStatus?.typeface = TypefaceHelper.getRegular()
                    }
                    mView?.isEnabled = false
                }

                ScoreAction.END -> {
                    viewBinding?.root?.isEnabled = true
                    viewBinding?.imgEnd?.isVisible = false
                    viewBinding?.lottieScoring?.isVisible = false
                    viewBinding?.imgTimer?.isVisible = false
                    if (canManage) {
                        mView?.isVisible = true
                        viewBinding?.txtStatus?.text =
                            LanguageController.getInstance()
                                .getString(R.string.vr_scoreboard_start_button)
                    } else {
                        mView?.isVisible = false
                    }
                }

                ScoreAction.OFF -> {
                    mView?.isVisible = false
                }
            }
            showResult(it)
        }
    }

    private fun showResult(it: ScoreStatusUpdate) {
        if (it.action == ScoreAction.START.value) {
            return
        }
        if (it.boardId?.isNotBlank() != true) {
            return
        }
        ScoreResultDialog.start(
            (getActivity() as AppCompatActivity?)?.supportFragmentManager
                ?: return, ArrayList(it.itemList)
        )
    }

    override fun onViewCreate() {
        super.onViewCreate()
        viewBinding?.imgEnd?.setOnClickListener {
            confirmDialog?.setTitleVisibility(View.GONE)
            confirmDialog?.setContent(
                LanguageController.getInstance()
                    .getString(R.string.vr_scoreboard_early_end)
            )
            confirmDialog?.setContentGravity(Gravity.CENTER)
            confirmDialog?.confirmCallback = callback@{
                scoreViewModel?.scoreBoardSwitch(
                    roomInfoViewModel?.getRoomId() ?: return@callback, ScoreAction.END
                )
            }
            confirmDialog?.show()
        }
        viewBinding?.root?.setOnClickListener {
            scoreViewModel?.scoreBoardSwitch(
                roomInfoViewModel?.getRoomId() ?: return@setOnClickListener, ScoreAction.START,
                scoreViewModel?.scoreStatusData?.value?.duration
            )
        }
    }

    override fun changeRoomMode(roomMode: Int, roomType: String?) {
        addSelf(roomMode, roomType)
    }

    override fun generateLayoutParams(roomMode: Int, roomType: String?): ViewGroup.LayoutParams {
        val params = ConstraintLayout.LayoutParams(
            ViewGroup.LayoutParams.WRAP_CONTENT,
            36.dp
        )
        params.topToBottom = R.id.anchor_list
        params.topMargin = -(20).dp
        params.startToStart = ConstraintLayout.LayoutParams.PARENT_ID
        params.endToEnd = ConstraintLayout.LayoutParams.PARENT_ID
        return params
    }

    override fun getViewBinding(inflater: LayoutInflater): ScoreLayoutCellBinding? {
        return ScoreLayoutCellBinding.inflate(inflater)
    }

    override fun handleEvent(event: Any) {

    }

    private fun startCountdown(startTime: Long, minute: Int) {
        countdownJob?.cancel()
        val total = minute * 60L
        val current = System.currentTimeMillis() / 1000
        val left = if (current - startTime >= 5) { // 5 是粗略估算的，服务端下发 IM 消息到客户端应该不会相差 5 秒吧
            total - (current - startTime)
        } else {
            total
        }
        if (left <= 0) {
            return
        }
        countdownJob = countdownScope.launch {
            for (i in left downTo 0) {
                viewBinding?.txtStatus?.text = ScoreDetailDialog.formatDuration(i)
                delay(1000)
            }
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        countdownJob?.cancel()
        countdownScope.cancel()
    }
}