package com.layaa.roomlogic.module.view

import android.animation.Animator
import android.animation.ValueAnimator
import android.animation.ValueAnimator.AnimatorUpdateListener
import android.content.Context
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.Paint
import android.text.TextUtils
import android.util.AttributeSet
import android.view.animation.LinearInterpolator
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.view.ViewCompat
import androidx.core.view.isVisible
import com.layaa.gift.giftlib.player.WebpAnimView
import com.layaa.libutils.LayoutUtils
import com.layaa.libutils.UIUtils
import com.layaa.libutils.module_thread.task.ThreadPool
import com.layaa.roomlogic.entity.RoomUserEntity
import java.util.LinkedList


/**
 *<AUTHOR>
 *@date 2021/7/26
 *@des
 **/
class RippleCircleLayout @JvmOverloads constructor(
        context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0
) : ConstraintLayout(context, attrs, defStyleAttr) {
    /**
     * 圆形波纹颜色最大透明度
     */
    private val MAX_RIPPLE_COLOR_ALPHA = 0.7f

    /**
     * 圆形波纹颜色最小透明度
     */
    private val MIN_RIPPLE_COLOR_ALPHA = 0.02f

    /**
     * 第二个圆形波纹延迟动画延迟值
     */
    private val SECOND_DELAY = 0.4f

    private lateinit var mPaint: Paint

    /**
     * 圆形波纹半径最小值
     */
    private var mRippleCircleMinRadius = 0f

    /**
     * 圆形波纹半径最大值
     */
    private var mRippleCircleMaxRadius = 0f

    /**
     * 第一个圆形波纹半径
     */
    private var mRippleCircleFirstRadius = 0f

    /**
     * 第二个圆形波纹半径
     */
    private var mRippleCircleSecondRadius = 0f

    /**
     * 波纹中心x轴坐标
     */
    private var mRippleCircleCenterX = 0f

    /**
     * 波纹中心y轴坐标
     */
    private var mRippleCircleCenterY = 0f

    /**
     * 第一个圆形波纹动画因子
     */
    private var mFirstInterpolatorValue = 0f

    /**
     * 第二个圆形波纹是否开始动画
     */
    private var mIsSecondStart = false

    /**
     * 圆形波纹扩散宽度
     */
    private var mRippleWidth = 0

    /**
     * 第二个圆形波纹动画因子List
     */
    private var mSecondFrameList: LinkedList<Float>? = null

    /**
     * 动画ValueAnimator
     */
    private var mValueAnimator: ValueAnimator? = null

    /**
     * 是否继续播放动画
     */
    private var mHasNextAnimation = false

    /**
     * 用户id，加密id
     */
    private var mUid: String? = null
    private var redColor = 255
    private var greenColor = 255
    private var blueColor = 255

    /**
     * 是否开启波纹动画
     */
    private var mIsEnableRipple = true

    private var stopRightNow = false

    private var webpAnimView: WebpAnimView? = null

    init {
        init(context)
    }


    private fun init(context: Context) {
        mPaint = Paint(Paint.ANTI_ALIAS_FLAG)
        mPaint.setColor(Color.WHITE)
        mPaint.setStyle(Paint.Style.FILL)
        mRippleWidth = UIUtils.getPixels(5F)
        mSecondFrameList = LinkedList()
    }

    fun setEnableRipple(isEnableRipple: Boolean) {
        mIsEnableRipple = isEnableRipple
    }

    /**
     * 设置画笔颜色,16进制转10进制
     *
     * @param rColor 红色通道
     * @param gColor 绿色通道
     * @param bColor 蓝色通道
     */
    fun setRippleRgbColorString(rColor: String, gColor: String, bColor: String) {
        try {
            redColor = rColor.toInt(16)
            greenColor = gColor.toInt(16)
            blueColor = bColor.toInt(16)
        } catch (e: Exception) {
            redColor = 255
            greenColor = 255
            blueColor = 255
        }
        mPaint.setColor(Color.argb(255, redColor, greenColor, blueColor))
        invalidate()
    }

    fun setRippleRgbColorString(colorStr:String){
        mPaint.setColor(Color.parseColor(colorStr))
        invalidate()
    }

    fun setRippleRgbColor(alpha: Int, red: Int, green: Int, blue: Int) {
        redColor = red
        greenColor = green
        blueColor = blue
        mPaint.setColor(Color.argb(alpha, redColor, greenColor, blueColor))
        invalidate()
    }

    override fun onMeasure(widthMeasureSpec: Int, heightMeasureSpec: Int) {
        super.onMeasure(widthMeasureSpec, heightMeasureSpec)
        mRippleCircleCenterX = measuredWidth / 2.0f
        mRippleCircleCenterY = measuredHeight / 2.0f
        mRippleCircleMinRadius = getChildAt(0).measuredWidth / 2.0f
        mRippleCircleMaxRadius = mRippleCircleMinRadius + mRippleWidth
        mRippleCircleFirstRadius = mRippleCircleMinRadius
        mRippleCircleSecondRadius = mRippleCircleMinRadius
    }

    override fun dispatchDraw(canvas: Canvas) {
        if (!mIsEnableRipple) {
            super.dispatchDraw(canvas)
            return
        }
        val mSecondInterpolatorValue: Float
        if ((mFirstInterpolatorValue > SECOND_DELAY || mIsSecondStart
                        || mValueAnimator == null || !mValueAnimator!!.isRunning)
                && mSecondFrameList?.isEmpty() != true) {
            mIsSecondStart = true
            mSecondInterpolatorValue = mSecondFrameList?.pollFirst() ?: 0F
        } else {
            mSecondInterpolatorValue = 0f
        }
        mRippleCircleFirstRadius = getRadius(mFirstInterpolatorValue)
        mRippleCircleSecondRadius = getRadius(mSecondInterpolatorValue)
        if (mRippleCircleFirstRadius > mRippleCircleSecondRadius) {
            if (stopRightNow) {
                mPaint.color = Color.TRANSPARENT
            } else {
                mPaint.color = getCircleAlphaColor(mFirstInterpolatorValue)
            }
            canvas.drawCircle(mRippleCircleCenterX, mRippleCircleCenterY, mRippleCircleFirstRadius, mPaint)

            //LogUtils.i("testRipple", "First=" + mFirstInterpolatorValue + " Second=" + mSecondInterpolatorValue);
            if (stopRightNow) {
                mPaint.color = Color.TRANSPARENT
            } else {
                mPaint.color = getCircleAlphaColor(mSecondInterpolatorValue)
            }
            if (mRippleCircleSecondRadius > mRippleCircleMinRadius) {
                canvas.drawCircle(mRippleCircleCenterX, mRippleCircleCenterY, mRippleCircleSecondRadius, mPaint)
            }
        } else {
            if (stopRightNow) {
                mPaint.color = Color.TRANSPARENT
            } else {
                mPaint.color = getCircleAlphaColor(mSecondInterpolatorValue)
            }
            if (mRippleCircleSecondRadius > mRippleCircleMinRadius) {
                canvas.drawCircle(mRippleCircleCenterX, mRippleCircleCenterY, mRippleCircleSecondRadius, mPaint)
            }

            //LogUtils.i("testRipple", "Second=" + mSecondInterpolatorValue + " First=" + mFirstInterpolatorValue);
            if (stopRightNow) {
                mPaint.color = Color.TRANSPARENT
            } else {
                mPaint.color = getCircleAlphaColor(mFirstInterpolatorValue)
            }
            if (mRippleCircleFirstRadius > mRippleCircleMinRadius) {
                canvas.drawCircle(mRippleCircleCenterX, mRippleCircleCenterY, mRippleCircleFirstRadius, mPaint)
            }
        }
        if (mValueAnimator != null && !mValueAnimator!!.isRunning) {
            //动画结束了
            if (mSecondFrameList?.isEmpty() != true) {
                //还有收尾动画
                ViewCompat.postInvalidateOnAnimation(this)
            } else if (mHasNextAnimation) {
                //收尾动画也结束了，还有动画要执行
                mHasNextAnimation = false
                post { startRippleAnimation() }
            } else if (mSecondInterpolatorValue > 0) {
                ViewCompat.postInvalidateOnAnimation(this)
            }
        }
        super.dispatchDraw(canvas)
    }

    /**
     * 计算圆形波纹半径
     *
     * @param interpolatorValue 差值
     * @return 半径
     */
    private fun getRadius(interpolatorValue: Float): Float {
        return (mRippleCircleMaxRadius - mRippleCircleMinRadius) * interpolatorValue + mRippleCircleMinRadius
    }

    private fun getCircleAlphaColor(interpolatorValue: Float): Int {
        val alpha = ((MAX_RIPPLE_COLOR_ALPHA
                - (MAX_RIPPLE_COLOR_ALPHA - MIN_RIPPLE_COLOR_ALPHA) * interpolatorValue) * 255).toInt()
        return Color.argb(alpha, redColor, greenColor, blueColor)
    }

    fun stopRippleAnimation() {
        if (mValueAnimator != null && mValueAnimator!!.isRunning) {
            mIsSecondStart = false
            mValueAnimator!!.repeatCount = 0
            mValueAnimator!!.cancel()
            if (mSecondFrameList?.isEmpty() != true) {
                mHasNextAnimation = true
                ViewCompat.postInvalidateOnAnimation(this)
            }
        }
    }

    fun stopAnim() {
        stopRightNow = true
        stopWebpAnim()
    }

    fun startRippleAnimation() {
        stopRightNow = false
        if (mValueAnimator == null) {
            initValueAnimator()
        }
        if (mValueAnimator!!.isRunning) {
            return
        } else if (mSecondFrameList?.isEmpty() != true) {
            //动画正在运行，或者还有收尾动画
            mHasNextAnimation = true
            ViewCompat.postInvalidateOnAnimation(this)
            return
        }
        mIsSecondStart = false
        mValueAnimator!!.cancel()
        mValueAnimator!!.repeatCount = 0
        mValueAnimator!!.start()
    }

    fun startRippleAnimationForever() {
        stopRightNow = false
        if (mValueAnimator == null) {
            initValueAnimator()
        }
        mIsSecondStart = false
        mValueAnimator!!.cancel()
        mValueAnimator!!.repeatCount = ValueAnimator.INFINITE
        mValueAnimator!!.start()
    }

    private fun initValueAnimator() {
        mValueAnimator = ValueAnimator.ofFloat(0f, 1.0f)
        val mAnimationDuration: Long = 1000
        mValueAnimator?.setDuration(mAnimationDuration)
        mValueAnimator?.setInterpolator(LinearInterpolator())
        mValueAnimator?.addUpdateListener(AnimatorUpdateListener { animation: ValueAnimator ->
            mFirstInterpolatorValue = animation.animatedValue as Float
            mSecondFrameList?.addLast(mFirstInterpolatorValue)
            ViewCompat.postInvalidateOnAnimation(this@RippleCircleLayout)
        })
        mValueAnimator?.addListener(object : Animator.AnimatorListener {
            override fun onAnimationStart(animation: Animator) {
                //no need to implement
            }

            override fun onAnimationEnd(animation: Animator) {
                mFirstInterpolatorValue = 0f
            }

            override fun onAnimationCancel(animation: Animator) {
                //no need to implement
            }

            override fun onAnimationRepeat(animation: Animator) {
                //no need to implement
            }
        })
    }

    fun setUid(uid: String?) {
        if (uid != null && uid != mUid) {
            mUid = uid
            if (isAttachedToWindow) {
                registerEventAction()
            }
        }
    }

    fun clearUid() {
        mUid = ""
        unregisterEventAction()
        clearValueAnimatorListener()
    }

    /*private val mOnUserVolumeChangeListener: OnUserVolumeChangeListener = object : OnUserVolumeChangeListener() {
        fun onVolumeChange(uid: String, volume: Float) {
            if (TextUtils.isEmpty(mUid) || mUid != uid) {
                return
            }
            val roomMemberInfo: RoomMemberInfo = RoomMembersHelper.getInstance().getByUid(mUid)
            if (roomMemberInfo != null && roomMemberInfo.mic != null && (!roomMemberInfo.mic.isMicOn() || roomMemberInfo.mic.isMute())) {
                //该成员不在麦上或者闭麦了，不显示波纹动画
                return
            }
            VChatKit.INSTANCE.getHandler().post(startRippleAnimationRunnable)
        }
    }*/

    private fun startWebpAnim(resource: String) {
        stopAnim()
        if (webpAnimView == null) {
            webpAnimView = WebpAnimView(context)
        }
        if (indexOfChild(webpAnimView) < 0) {
            addView(
                webpAnimView,
                0,
                LayoutUtils.createFrame(LayoutUtils.MATCH_PARENT, LayoutUtils.MATCH_PARENT)
            )
        }
        webpAnimView?.start(resource, 1)
    }

    private fun stopWebpAnim() {
        webpAnimView?.stop()
        webpAnimView?.isVisible = false
    }

    fun startAnim(user: RoomUserEntity?) {
        if (user?.ripple?.isNotBlank() == true) {
            startWebpAnim(user.ripple)
        } else {
            stopWebpAnim()
            ThreadPool.runOnUiThread(startRippleAnimationRunnable)
        }
    }

    private val startRippleAnimationRunnable = Runnable { startRippleAnimation() }

    private fun registerEventAction() {
        if (TextUtils.isEmpty(mUid)) {
            return
        }
//        RoomLinkSdkHelper.getInstance().addOnUserVolumeChangeListener(mOnUserVolumeChangeListener)
    }

    private fun unregisterEventAction() {
//        RoomLinkSdkHelper.getInstance().removeOnUserVolumeChangeListener(mOnUserVolumeChangeListener)
    }

    override fun onAttachedToWindow() {
        super.onAttachedToWindow()
        registerEventAction()
    }

    override fun onDetachedFromWindow() {
        super.onDetachedFromWindow()
        unregisterEventAction()
        clearValueAnimatorListener()
    }

    private fun clearValueAnimatorListener() {
        if (mValueAnimator != null) {
            mValueAnimator!!.cancel()
            mValueAnimator!!.removeAllUpdateListeners()
            mValueAnimator!!.removeAllListeners()
            mValueAnimator = null
            mSecondFrameList?.clear()
        }
    }
}