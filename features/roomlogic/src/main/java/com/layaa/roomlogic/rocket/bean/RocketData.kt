package com.layaa.roomlogic.rocket.bean

import android.os.Parcelable
import androidx.annotation.Keep
import kotlinx.parcelize.Parcelize

@Keep
@Parcelize
data class RocketData(
    val enabled: Boolean = true, // 当前大区是否开启了爆火箭
    val roundId: Long = -1,
    val state: Int = -1,  // 状态1——进行中，2——已爆炸，等待抽奖，3——可抽奖
    val treasureBoxIconUrl: String = "",//: "宝箱图片",
    val rocketIconUrl: String = "",//: "火箭图片",
    val effectUrl: String = "",//: "动效url",
    val duration: Long = 0, // 当前阶段持续时间
    val maxLevel: Int = 0,  // 本次开奖最高等级
    val currentContribution: Long = 0,  // 当前等级的贡献值
    val requiredContribution: Long = 0,//: 1000   // 当前等级总共需要的贡献值
    val top1user: TopUserData? = null//: 1000   // 当前等级总共需要的贡献值
) : Parcelable


@Keep
@Parcelize
data class TopUserData(val uid: String, val nickName: String, val photo: String) : Parcelable