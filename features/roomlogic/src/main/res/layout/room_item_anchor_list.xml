<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:tools="http://schemas.android.com/tools"
    android:paddingHorizontal="4.5dp"
    android:clipChildren="false">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:clipChildren="false"
        android:maxWidth="100dp">

        <View
            android:id="@+id/viewBg"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            tools:background="@drawable/room_bg_anchor_pk_left" />

        <com.layaa.roomlogic.module.view.RippleCircleLayout
            android:id="@+id/ripple"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:clipChildren="false"
            app:layout_constraintWidth_default="percent"
            app:layout_constraintDimensionRatio="1:1"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/layoutAvatar"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:clipChildren="false"
                app:layout_constraintDimensionRatio="1:1"
                app:layout_constraintWidth_default="percent"
                app:layout_constraintWidth_percent="0.85"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintBottom_toBottomOf="parent">

                <com.layaa.widget.AvatarView
                    android:clipChildren="false"
                    android:id="@+id/avatar"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_gravity="center"
                    android:scaleType="centerCrop"
                    tools:background="@drawable/circle_white"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <androidx.constraintlayout.widget.Guideline
                    android:id="@+id/guidelineEnd"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    app:layout_constraintGuide_percent="0.9" />

                <androidx.constraintlayout.widget.Guideline
                    android:id="@+id/guidelineBottom"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    app:layout_constraintGuide_percent="0.9" />

                <ImageView
                    android:id="@+id/micStatus"
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    app:layout_constraintWidth_default="percent"
                    app:layout_constraintWidth_percent="0.23"
                    app:layout_constraintDimensionRatio="1:1"
                    android:scaleType="centerCrop"
                    android:src="@drawable/room_icon_anchor_mic_close"
                    android:visibility="gone"
                    tools:visibility="visible"
                    app:layout_constraintEnd_toEndOf="@id/guidelineEnd"
                    app:layout_constraintBottom_toBottomOf="@id/guidelineBottom" />

            </androidx.constraintlayout.widget.ConstraintLayout>

            <com.layaa.roomlogic.module.view.EmojiAnimateView
                android:id="@+id/viewEmojiAnim"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:visibility="gone"
                tools:visibility="visible"
                app:layout_constraintStart_toStartOf="@id/layoutAvatar"
                app:layout_constraintEnd_toEndOf="@id/layoutAvatar"
                app:layout_constraintTop_toTopOf="@id/layoutAvatar"
                app:layout_constraintBottom_toBottomOf="@id/layoutAvatar" />

        </com.layaa.roomlogic.module.view.RippleCircleLayout>

        <TextView
            android:id="@+id/nickname"
            android:layout_width="0dp"
            android:layout_height="20dp"
            android:textColor="#ffffff"
            android:textSize="13sp"
            android:ellipsize="end"
            android:gravity="center"
            android:maxLines="1"
            android:paddingStart="2dp"
            android:paddingEnd="2dp"
            android:singleLine="true"
            android:layout_marginTop="-6dp"
            tools:text="nicknamenickname"
            app:layout_constraintEnd_toEndOf="@id/ripple"
            app:layout_constraintStart_toStartOf="@id/ripple"
            app:layout_constraintTop_toBottomOf="@+id/ripple" />

        <com.layaa.roomlogic.module.score.ScoreAnchorValueView
            android:id="@+id/scoreValueView"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:layout_constraintTop_toBottomOf="@id/nickname"
            app:layout_constraintStart_toStartOf="@id/nickname"
            app:layout_constraintEnd_toEndOf="@id/nickname"
            android:layout_marginTop="3dp"
            android:visibility="invisible"
            tools:visibility="visible" />

        <com.layaa.roomlogic.module.pk.PKAnchorValueView
            android:id="@+id/pkValueView"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:layout_constraintTop_toBottomOf="@id/nickname"
            app:layout_constraintStart_toStartOf="@id/nickname"
            app:layout_constraintEnd_toEndOf="@id/nickname"
            app:layout_constraintBottom_toBottomOf="parent"
            android:layout_marginVertical="3dp"
            android:visibility="invisible"
            tools:visibility="visible" />

        <com.layaa.roomlogic.module.pk.PKAnchorWeaponView
            android:id="@+id/pkWeaponView"
            android:layout_width="45dp"
            android:layout_height="45dp"
            android:visibility="invisible"
            tools:visibility="visible"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintStart_toStartOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>

</FrameLayout>