<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@id/anchor_list"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:clipChildren="false">

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/recyclerAnchor"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:paddingHorizontal="7.5dp"
        android:overScrollMode="never"
        android:clipChildren="false" />

    <ImageView
        android:id="@+id/imgFold"
        android:layout_width="wrap_content"
        android:layout_height="21dp"
        android:src="@drawable/room_icon_anchor_fold"
        app:layout_constraintTop_toBottomOf="@id/recyclerAnchor"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_marginTop="9dp"
        android:layout_marginEnd="12dp" />

</androidx.constraintlayout.widget.ConstraintLayout>