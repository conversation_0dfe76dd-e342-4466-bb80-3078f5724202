<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">


    <View
        android:id="@+id/topBg"
        android:layout_width="0dp"
        android:layout_height="69dp"
        android:layout_margin="15dp"
        android:background="@drawable/bg_15_fffdf5"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />


    <ImageView
        android:id="@+id/roomPic"
        android:layout_width="51dp"
        android:layout_height="51dp"
        android:layout_marginStart="9dp"
        android:src="@drawable/room_icon_my_room_placeholder"
        app:layout_constraintBottom_toBottomOf="@+id/topBg"
        app:layout_constraintStart_toStartOf="@+id/topBg"
        app:layout_constraintTop_toTopOf="@+id/topBg" />

    <com.layaa.widget.MarqueeTextView
        android:id="@+id/roomName"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="9dp"
        android:layout_marginEnd="5dp"
        android:ellipsize="marquee"
        android:focusable="true"
        android:gravity="start"
        android:text="@string/create_room"
        android:textColor="@color/color_202530"
        android:textSize="13sp"
        app:fontFamily="@font/montserrat_semi_bold"
        app:layout_constrainedWidth="true"
        app:layout_constraintHorizontal_bias="0"
        app:layout_constraintBottom_toTopOf="@id/userCount"
        app:layout_constraintEnd_toEndOf="@id/topBg"
        app:layout_constraintStart_toEndOf="@+id/roomPic"
        app:layout_constraintTop_toTopOf="@+id/topBg"
        app:layout_constraintVertical_chainStyle="packed" />

    <TextView
        android:id="@+id/userCount"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="3dp"
        android:layout_marginTop="7.5dp"
        android:textColor="#1FB58D"
        android:textSize="12sp"
        android:text=" "
        app:fontFamily="@font/montserrat_semi_bold"
        app:layout_constraintBottom_toBottomOf="@id/topBg"
        app:layout_constraintStart_toEndOf="@id/lottie"
        app:layout_constraintTop_toBottomOf="@+id/roomName"
        tools:text="0" />

    <com.layaa.widget.svga.CompatSVGAImageView
        android:id="@+id/lottie"
        android:layout_width="15dp"
        android:layout_height="15dp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@id/userCount"
        app:layout_constraintStart_toStartOf="@+id/roomName"
        app:layout_constraintTop_toTopOf="@id/userCount" />

    <ImageView
        android:id="@+id/imgMine"
        android:layout_width="40dp"
        android:layout_height="40dp"
        android:layout_marginTop="-4dp"
        android:layout_marginEnd="-4dp"
        android:src="@drawable/icon_room_me"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="@id/topBg"
        app:layout_constraintTop_toTopOf="@id/topBg"
        app:srcReverse="true"
        tools:visibility="visible" />

    <TextView
        android:id="@+id/roomMineTv"
        android:layout_width="26.87dp"
        android:layout_height="26.87dp"
        android:layout_marginTop="2dp"
        android:layout_marginEnd="2dp"
        android:rotation="45"
        android:text="@string/vr_wallpaper_mine"
        android:textColor="#FFFDF1"
        android:textSize="10sp"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="@id/topBg"
        app:layout_constraintTop_toTopOf="@id/topBg"
        tools:visibility="visible"
        tools:ignore="SmallSp" />

    <View
        android:id="@+id/selectBg"
        android:layout_width="0dp"
        android:layout_height="33dp"
        android:layout_marginTop="12dp"
        android:background="@drawable/bg_22_265d50"
        app:layout_constraintEnd_toEndOf="@id/topBg"
        app:layout_constraintStart_toStartOf="@id/topBg"
        app:layout_constraintTop_toBottomOf="@id/topBg" />

    <TextView
        android:id="@+id/recently"
        android:layout_width="0dp"
        android:layout_height="27dp"
        android:layout_marginStart="3dp"
        android:background="@drawable/bg_room_list_tab"
        android:gravity="center"
        android:text="@string/room_list_menu_recently"
        android:textColor="@color/color_room_list_tab"
        app:fontFamily="@font/montserrat_medium"
        app:layout_constraintBottom_toBottomOf="@id/selectBg"
        app:layout_constraintEnd_toStartOf="@id/following"
        app:layout_constraintHeight_percent="0.49"
        app:layout_constraintStart_toStartOf="@id/selectBg"
        app:layout_constraintTop_toTopOf="@id/selectBg" />

    <TextView
        android:id="@+id/following"
        android:layout_width="0dp"
        android:layout_height="27dp"
        android:layout_marginEnd="3dp"
        android:background="@drawable/bg_room_list_tab"
        android:gravity="center"
        android:text="@string/room_list_menu_following"
        android:textColor="@color/color_room_list_tab"
        app:fontFamily="@font/montserrat_medium"
        app:layout_constraintBottom_toBottomOf="@id/selectBg"
        app:layout_constraintEnd_toEndOf="@id/selectBg"
        app:layout_constraintHeight_percent="0.49"
        app:layout_constraintStart_toEndOf="@id/recently"
        app:layout_constraintTop_toTopOf="@id/selectBg" />

    <com.layaa.roomlogic.liveroom.NestedScrollableHost
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/selectBg">

        <androidx.viewpager2.widget.ViewPager2
            android:id="@+id/pager"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:overScrollMode="never" />
    </com.layaa.roomlogic.liveroom.NestedScrollableHost>
</androidx.constraintlayout.widget.ConstraintLayout>