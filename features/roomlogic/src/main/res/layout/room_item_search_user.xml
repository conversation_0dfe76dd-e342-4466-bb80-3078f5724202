<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="60dp"
    xmlns:tools="http://schemas.android.com/tools">


    <com.layaa.roomlogic.module.view.RippleCircleLayout
        android:id="@+id/ripple"
        android:layout_width="52.5dp"
        android:layout_height="52.5dp"
        android:layout_gravity="center"
        android:layout_marginStart="15dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:id="@+id/photo"
            android:layout_width="42dp"
            android:layout_height="42dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            android:scaleType="centerCrop" />
    </com.layaa.roomlogic.module.view.RippleCircleLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/lottieView"
        android:layout_width="wrap_content"
        android:layout_height="12dp"
        android:background="@drawable/shape_675_42d_51d"
        android:paddingHorizontal="3dp"
        app:layout_constraintBottom_toBottomOf="@+id/ripple"
        app:layout_constraintEnd_toEndOf="@+id/ripple"
        app:layout_constraintStart_toStartOf="@+id/ripple"
        app:lottie_colorFilter="@color/color_white"
        app:lottie_fileName="lottie/room_list_online.json">

        <com.airbnb.lottie.LottieAnimationView
            android:id="@+id/lottie"
            android:layout_width="9dp"
            android:layout_height="9dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:lottie_colorFilter="@color/color_white" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/chat"
            android:textColor="#ffffffff"
            android:textSize="8sp"
            app:fontFamily="@font/montserrat_medium"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toEndOf="@id/lottie"
            app:layout_constraintTop_toTopOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <com.layaa.libui.widget.nickname.NicknameTextView
        android:id="@+id/nickname"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="6dp"
        android:ellipsize="end"
        android:singleLine="true"
        tools:text="333"
        android:textColor="@color/color_202530"
        android:textSize="14sp"
        app:layout_constraintWidth_max="153dp"
        app:gradientOrientation="horizontal"
        app:fontFamily="@font/montserrat_medium"
        app:layout_constraintBottom_toTopOf="@id/uid"
        app:layout_constraintStart_toEndOf="@+id/ripple"
        app:layout_constraintTop_toTopOf="parent" />

    <com.layaa.libui.widget.PrettyUidView
        android:id="@+id/uid"
        android:layout_width="wrap_content"
        android:layout_height="21dp"
        app:fontFamily="@font/montserrat_regular"
        app:idColor="@color/color_86909C"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="@id/nickname"
        app:layout_constraintTop_toBottomOf="@id/nickname"
        app:textSize="12dp" />

    <ImageView
        android:id="@+id/countryFlag"
        android:layout_width="21dp"
        android:layout_height="15dp"
        android:layout_marginStart="3dp"
        app:layout_constraintBottom_toBottomOf="@+id/nickname"
        app:layout_constraintStart_toEndOf="@+id/nickname"
        app:layout_constraintTop_toTopOf="@+id/nickname" />

    <com.layaa.libui.widget.GenderAgeView
        android:id="@+id/gender"
        android:layout_width="wrap_content"
        android:layout_height="15dp"
        android:layout_marginStart="3dp"
        app:layout_constraintBottom_toBottomOf="@+id/countryFlag"
        app:layout_constraintStart_toEndOf="@+id/countryFlag"
        app:layout_constraintTop_toTopOf="@+id/countryFlag" />

    <com.layaa.libui.widget.UserLevelView
        android:id="@+id/userLevelView"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="3dp"
        android:scaleType="centerCrop"
        app:layout_constraintBottom_toBottomOf="@id/gender"
        app:layout_constraintStart_toEndOf="@id/gender"
        app:layout_constraintTop_toTopOf="@id/gender" />

    <ImageView
        android:id="@+id/flowIv"
        android:layout_width="24dp"
        android:layout_height="24dp"
        android:layout_marginEnd="12dp"
        android:src="@drawable/icon_un_flow"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>