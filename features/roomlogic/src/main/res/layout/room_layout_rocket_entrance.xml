<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:visibility="gone">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/openBxView"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">
        <ImageView
            android:id="@+id/bxIv"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:src="@drawable/icon_bx1"
            android:scaleType="fitXY"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/bxCountdownTV"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="@drawable/bg_bx_countdown"
            android:paddingHorizontal="4dp"
            android:paddingVertical="1.5dp"
            android:textColor="#ffffff"
            android:textSize="10sp"
            android:gravity="center"
            android:visibility="gone"
            app:fontFamily="@font/montserrat_medium"
            app:layout_constraintBottom_toBottomOf="@id/bxIv"
            app:layout_constraintEnd_toEndOf="@id/bxIv"
            app:layout_constraintStart_toStartOf="@id/bxIv"
            app:layout_constraintWidth_min="35dp"
            tools:text="2:34" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/rocketView"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">
        <ImageView
            android:id="@+id/rocketIv"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:src="@drawable/icon_rocket_lv_1"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <!-- 横向进度条 -->
        <ProgressBar
            android:id="@+id/rocketProgressBar"
            style="?android:attr/progressBarStyleHorizontal"
            android:layout_width="0dp"
            android:layout_height="12dp"
            android:layout_gravity="center_vertical"
            app:layout_constraintStart_toStartOf="@id/rocketIv"
            app:layout_constraintEnd_toEndOf="@id/rocketIv"
            app:layout_constraintBottom_toBottomOf="@id/rocketIv"
            android:max="100"
            android:progress="0"
            android:progressDrawable="@drawable/progerss_bar_rocket_enter" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.constraintlayout.widget.ConstraintLayout>