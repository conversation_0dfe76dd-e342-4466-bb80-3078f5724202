<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <ImageView
        android:id="@+id/imgBg"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:adjustViewBounds="true"
        tools:src="@drawable/room_bg_pk_result_decoration_left" />


    <ImageView
        android:id="@+id/imgTop"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintBottom_toTopOf="@id/layoutContent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        tools:src="@drawable/room_icon_pk_result_top_left"
        android:adjustViewBounds="true" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/layoutContent"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintWidth_default="percent"
        app:layout_constraintWidth_percent="0.92"
        app:layout_constraintDimensionRatio="1035:1161"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"

        tools:background="@drawable/room_bg_pk_result_content_left">

        <androidx.constraintlayout.widget.Group
            android:id="@+id/groupContent"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:constraint_referenced_ids="imgFlagLeft,imgFlagRight,layoutProgress,imgTop1Avatar,imgTop1Headwear,txtTop1Nickname,imgTop1ScoreBg,imgDiamond,txtTop1Score," />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/layoutProgress"
            android:layout_width="0dp"
            android:layout_height="27dp"
            app:layout_constraintStart_toStartOf="@id/imgFlagLeft"
            app:layout_constraintEnd_toEndOf="@id/imgFlagRight"
            app:layout_constraintTop_toTopOf="@id/imgFlagLeft"
            android:layout_marginHorizontal="17.5dp"
            app:layout_constraintBottom_toBottomOf="@id/imgFlagLeft"
            android:background="#8F1C1010">

            <androidx.constraintlayout.widget.Guideline
                android:id="@+id/guidelineProgress"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                app:layout_constraintGuide_percent="0.65" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/txtProgressLeft"
                android:layout_width="0dp"
                android:layout_height="21dp"
                android:paddingStart="24dp"
                android:paddingEnd="5dp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toStartOf="@id/guidelineProgress"
                android:gravity="center_vertical|start"
                android:background="#FFC41F1F"
                app:autoSizeTextType="uniform"
                app:autoSizeMaxTextSize="13sp"
                app:autoSizeMinTextSize="7sp"
                app:autoSizeStepGranularity="1sp"
                android:maxLines="1"
                android:ellipsize="none"
                android:textColor="@color/color_white"
                app:fontFamily="@font/montserrat_semi_bold"
                tools:text="312312312" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/txtProgressRight"
                android:layout_width="0dp"
                android:layout_height="21dp"
                android:paddingStart="5dp"
                android:paddingEnd="24dp"
                app:layout_constraintStart_toEndOf="@id/guidelineProgress"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                android:gravity="center_vertical|end"
                android:background="#FF6E737B"
                android:textColor="@color/color_white"
                app:fontFamily="@font/montserrat_semi_bold"
                app:autoSizeTextType="uniform"
                app:autoSizeMaxTextSize="13sp"
                app:autoSizeMinTextSize="7sp"
                app:autoSizeStepGranularity="1sp"
                android:maxLines="1"
                android:ellipsize="none"
                tools:text="1235456" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <ImageView
            android:id="@+id/imgFlagLeft"
            android:layout_width="36dp"
            android:layout_height="36dp"
            tools:src="@drawable/room_icon_pk_result_flag_win_left"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            android:layout_marginTop="27dp"
            android:layout_marginStart="64.5dp" />

        <ImageView
            android:id="@+id/imgFlagRight"
            android:layout_width="36dp"
            android:layout_height="36dp"
            tools:src="@drawable/room_icon_pk_result_flag_lose_right"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            android:layout_marginTop="27dp"
            android:layout_marginEnd="64.5dp" />

        <ImageView
            android:id="@+id/imgTop1Avatar"
            android:layout_width="0dp"
            android:layout_height="0dp"
            app:layout_constraintDimensionRatio="1:1"
            app:layout_constraintWidth_default="percent"
            app:layout_constraintWidth_percent="0.26"
            tools:src="@drawable/default_avatar"
            app:layout_constraintTop_toTopOf="@id/imgTop1Headwear"
            app:layout_constraintStart_toStartOf="@id/imgTop1Headwear"
            app:layout_constraintEnd_toEndOf="@id/imgTop1Headwear"
            app:layout_constraintBottom_toBottomOf="@id/imgTop1Headwear" />

        <ImageView
            android:id="@+id/imgTop1Headwear"
            android:layout_width="0dp"
            android:layout_height="0dp"
            app:layout_constraintDimensionRatio="1:1"
            app:layout_constraintWidth_default="percent"
            app:layout_constraintWidth_percent="0.40"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@id/imgFlagLeft"
            tools:src="@drawable/room_icon_pk_result_top1_headwear_left" />

        <TextView
            android:id="@+id/txtTop1Nickname"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textSize="18sp"
            android:textColor="@color/color_white"
            app:fontFamily="@font/montserrat_medium"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintBottom_toBottomOf="@id/imgTop1Headwear"
            android:layout_marginBottom="-12dp"
            app:layout_constrainedWidth="true"
            android:layout_marginHorizontal="64dp"
            android:maxLines="1"
            android:ellipsize="end"
            tools:text="Nickname" />

        <ImageView
            android:id="@+id/imgTop1ScoreBg"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            tools:src="@drawable/room_bg_pk_result_top1_score_left"
            app:layout_constraintStart_toStartOf="@id/imgDiamond"
            app:layout_constraintEnd_toEndOf="@id/txtTop1Score"
            app:layout_constraintTop_toTopOf="@id/imgDiamond"
            app:layout_constraintBottom_toBottomOf="@id/imgDiamond" />

        <ImageView
            android:id="@+id/imgDiamond"
            android:layout_width="18dp"
            android:layout_height="18dp"
            android:src="@drawable/wallet_icon_diamond"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/txtTop1Nickname"
            app:layout_constraintEnd_toStartOf="@id/txtTop1Score"
            app:layout_constraintHorizontal_chainStyle="packed"
            android:layout_marginTop="8.5dp" />

        <TextView
            android:id="@+id/txtTop1Score"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:layout_constraintStart_toEndOf="@id/imgDiamond"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@id/imgDiamond"
            app:layout_constraintBottom_toBottomOf="@id/imgDiamond"
            android:textSize="15sp"
            android:textColor="#FFFFD38C"
            android:layout_marginStart="3dp"
            app:fontFamily="@font/montserrat_medium"
            tools:text="123" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recyclerMember"
            android:layout_width="wrap_content"
            android:layout_height="75dp"
            app:layout_constrainedWidth="true"
            android:layout_marginHorizontal="50dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@id/imgTop1ScoreBg" />

        <TextView
            android:id="@+id/txtMemberEmpty"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textSize="13sp"
            android:textColor="#FFF9FFF5"
            android:text="@string/pk_result_member_empty"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            android:layout_marginHorizontal="55dp"
            app:layout_constrainedWidth="true"
            app:layout_constraintTop_toBottomOf="@id/imgTop1ScoreBg"
            android:layout_marginTop="10dp"
            android:gravity="center" />

        <TextView
            android:id="@+id/txtDraw"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="#FFF9FFF5"
            android:textSize="15sp"
            app:fontFamily="@font/montserrat_medium"
            android:text="@string/pk_result_draw_content"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            android:layout_marginTop="159dp"
            tools:visibility="visible"
            android:visibility="gone" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <com.layaa.libui.widget.ColorfulTextView
        android:id="@+id/txtTitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="@id/layoutContent"
        app:layout_constraintBottom_toTopOf="@id/layoutContent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:gradientColors="@array/pk_result_title"
        app:gradientOrientation="vertical"
        android:shadowColor="#FF603722"
        android:shadowDy="2"
        android:shadowDx="0"
        app:shadowRadius="1dp"
        app:outlineColor="#FFFFDA21"
        app:outlineWidth="1dp"
        android:textSize="42sp"
        app:fontFamily="@font/montserrat_black"
        tools:text="@string/pk_result_win" />

    <ImageView
        android:id="@+id/imgClose"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:src="@drawable/room_icon_pk_result_close"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/layoutContent"
        android:layout_marginTop="15dp" />


</androidx.constraintlayout.widget.ConstraintLayout>