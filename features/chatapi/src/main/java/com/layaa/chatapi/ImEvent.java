package com.layaa.chatapi;

/**
 * <AUTHOR>
 * @date 2021/4/19
 * @des
 **/
public interface ImEvent {

    String EVENT_ID = "eventId";

    interface CustomSysMsg {

        //用户账号余额改动
        int TYPE_BALANCE = 206;

        //用户升级
        int TYPE_USER_LEVEL_UP = 207;

        //设备封禁，用户退出登录，服务器在用户登录接口进行之后的拦截
        int TYPE_DEVICE_BANNED = 209;

        //被封禁
        int TYPE_BANNED = 1017;

        // 用户关系变更
        int RELATION_CHANGE = 1001;

        /**
         * 房间心跳
         */
        int TYPE_ROOM_HEART = 2001;
        /**
         * 房间邀请上麦
         */
        int TYPE_ROOM_INVITE_MIC = 2006;
        /**
         * 禁止评论
         */
        int TYPE_ROOM_FORBID_COMMENT = 2011;
        int TYPE_MATCH_GAME_ADD = 101001;//匹配中，队伍中队员变化的IM消息 原3001;
        int TYPE_MATCH_GAME_REMOVE = 3002;
        int TYPE_MATCH_GAME_SUC = 101002;//匹配成功，下发匹配结果 原3003;
        int TYPE_INVITE_GAME_LEAVE = 3004;
        int TYPE_INVITE_OWN_LEAVE = 3005;
        int TYPE_MATCH_CAN_CANCEL = 101003;//匹配超时，下发可取消消息 原3006;
    }

    interface ChannelMsg {
        int ROOM_HEART = 100;

        int LIVE_HEART = 101;
    }

    interface CustomP2pMsg {

        /**
         * emoji
         */
        int TYPE_EMOJI = 101;

        int TYPE_LUDO_INVITE = 102;//游戏邀请

        int TYPE_ROOM_INVITE = 103;//房间分享

        //todo 暂定
        int TYPE_LIVE_INVITE = 30002;//直播邀请
        int TYPE_C2C_IMG = 104;//私聊图片走自定义
        int TYPE_C2C_AUDIO = 105;//私聊语音消息走自定义
        int TYPE_AGENCY_INVITE = 210;//公会邀请

        int TYPE_GIFT_MSG = 2518;//礼物消息

        int TYPE_AGENT_TRANSFERRED = 1806; // 代理已转账

        int TYPE_AGENT_RECHARGED = 1807; // 用户已到账
        int TYPE_OFFICIAL = 50001; // 官方消息
        int TYPE_SEND_BACKPACK = 60001; // 背包转赠消息


        /**
         * 解除禁言
         */
        int LIVE_UN_MUTE_MOMENT = 3007;
    }

    public interface CustomRoomMsg {
        int TYPE_TEXT = 301;
        int MSG_EXPRESSION = 302;
        int MSG_QUICK = 303;
        int TYPE_NOTICE = 304;
        int TYPE_OFFICIAL = 305;
        int TYPE_NORMAL_TIPS = 306;
        int TYPE_IMAGE = 307;
        int TYPE_EMOJI = 308;

        int TYPE_SYNC_MIC = 2102;
        int TYPE_ENTER_ROOM = 2003;//进场横幅

        int TYPE_SYNC_ROOM_INFO = 2005;

        int TYPE_CLEAR_SCREEN = 2016;
        int TYPE_USER_MANAGER = 2101;
        int TYPE_MIC_MANAGER = 2100;

        int TYPE_MIC_COUNT_CHANGE = 2103;

        int TYPE_TOP_LIST_CHANGE = 2104;
        int TYPE_ROOM_RANK_VALUE_CHANGE = 2111;//房间送礼总流水变化

        /**
         * 增加删除 管理员
         */
        int TYPE_ROOM_MANAGER_CHANGE = 2014;

        int TYPE_ROOM_CHANGE_INFO = 2105;

        int TYPE_ROOM_SCORE_STATUS_UPDATE = 2106;
        int TYPE_ROOM_SCORE_VALUE_UPDATE = 2107;

        /**
         * 猜拳
         */
        int TYPE_ROOM_FINGER_GAME = 2017;

        /**
         * 老虎机
         */
        int TYPE_ROOM_SLOT_MACHINE = 2018;

        /**
         * 骰子
         */
        int TYPE_ROOM_DICE = 2019;

        /**
         * 送礼消息
         */
        int GIFT_TEXT_MESSAGE = 2516;

        /**
         * 送礼特效
         */
        int GIFT_EFFECT_MESSAGE = 2517;

        /**
         * 无连送情况下，送礼消息和送礼特效都用这一个 (Luma 代码兼容)
         */
        int GIFT_MESSAGE = 70001;

        /**
         * 幸运礼物
         */
        int GIFT_BLIND_MESSAGE = 2518;

        /**
         * 跑马灯礼物
         */
        int ROOM_MARQUEE_MESSAGE = 832;

        int USER_INFO_CHANGE = 836;
        /**
         * 用户等级升级
         */
        int USER_LEVEL_UP = 833;

        int LIVE_HEART = 10001;

        int FLOAT_BANNER = 2108;

        int ROOM_CLOSE = 2109;

        int ROOM_EXIT = 2004;

        int ROOM_THEME_CHANGE = 2110;

        int ONLINE_COUNT_CHANGE = 30013;
        int ROOM_ROCKET_STATUS_CHANGE = 2207;//爆火箭状态变更

        int PK_VALUE_CHANGE = 2201;
        int PK_OPEN = 2202;
        int PK_START = 2203;
        int PK_END = 2204;
        int PK_CLOSE = 2205;

        int IM_SYSTEM_TEXT = 2206;
        int IM_SYSTEM_USER_TEXT = 2208;//公屏代发用户消息
    }
}
