<?xml version="1.0" encoding="utf-8"?>
<resources>

    <declare-styleable name="SkinCompatTextHelper">
        <attr name="android:text" />
        <attr name="android:textStyle" />
        <attr name="android:hint" />
        <attr name="android:drawableStart" />
        <attr name="android:drawableEnd" />
        <attr name="android:drawableTop" />
        <attr name="android:drawableBottom" />
        <attr name="drawableStartCompat" format="reference" />
        <attr name="drawableEndCompat" format="reference" />
        <attr name="drawableTopCompat" format="reference" />
        <attr name="drawableBottomCompat" format="reference" />
        <attr name="mediumTextStyle" format="boolean" />
        <attr name="regularTextStyle" format="boolean" />
    </declare-styleable>

    <declare-styleable name="SkinCompatEditTextHelper">
        <attr name="android:text" />
        <attr name="android:textStyle" />
        <attr name="android:hint" />
    </declare-styleable>
    <declare-styleable name="SkinBackgroundHelper">
        <attr name="android:background" />
    </declare-styleable>

    <declare-styleable name="SkinCompatImageHelper">
        <attr name="android:src" />
        <attr name="srcCompat" />
        <attr name="srcRtl" format="reference" />
        <attr name="srcReverse" format="boolean" />
    </declare-styleable>

    <declare-styleable name="PressEffectHelper">
        <attr name="enable_effect_press" format="boolean" />
    </declare-styleable>

    <declare-styleable name="ForceLtrHelper">
        <attr name="force_direction" format="boolean" />
    </declare-styleable>
</resources>