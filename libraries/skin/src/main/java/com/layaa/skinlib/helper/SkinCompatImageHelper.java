package com.layaa.skinlib.helper;

import android.content.res.TypedArray;
import android.graphics.Canvas;
import android.util.AttributeSet;
import android.view.View;
import android.widget.ImageView;

import com.layaa.skinlib.R;
import com.layaa.skinlib.SkinKit;
import com.layaa.libutils.module_log.LogUtils;

/**
 * <AUTHOR>
 * @date 2020-01-07
 * @des
 **/
public class SkinCompatImageHelper extends BaseSkinCompatHelper {
    private static final String TAG = SkinCompatImageHelper.class.getSimpleName();
    private final ImageView mView;
    private int mSrcResId = INVALID_ID;
    private int mArSrcResId = INVALID_ID;
    private boolean isReverse = false;

    public SkinCompatImageHelper(ImageView imageView) {
        mView = imageView;
    }

    @Override
    public void loadFromAttributes(AttributeSet attrs, int defStyleAttr) {
        TypedArray a = null;
        try {
            a = mView.getContext().obtainStyledAttributes(attrs, R.styleable.SkinCompatImageHelper, defStyleAttr, 0);
            mSrcResId = a.getResourceId(R.styleable.SkinCompatImageHelper_android_src, INVALID_ID);
            mArSrcResId = a.getResourceId(R.styleable.SkinCompatImageHelper_srcRtl, INVALID_ID);
            isReverse = a.getBoolean(R.styleable.SkinCompatImageHelper_srcReverse, false);
        } finally {
            if (a != null) {
                a.recycle();
            }
        }
        applySkin();
    }

    @Override
    public void applySkin() {
        try {
            if (SkinKit.getInstance().isRtl()) {
                mView.setLayoutDirection(View.LAYOUT_DIRECTION_RTL);
                mView.setTextDirection(View.TEXT_DIRECTION_RTL);
                if (mArSrcResId != INVALID_ID) {
                    mView.setImageResource(mArSrcResId);
                } else {
                    if (isReverse) {
                        mView.setScaleX(-1);
                    }
                }
            } else {
                mView.setLayoutDirection(View.LAYOUT_DIRECTION_LTR);
                mView.setTextDirection(View.TEXT_DIRECTION_LTR);
                if (mSrcResId != INVALID_ID) {
                    mView.setImageResource(mSrcResId);
                }
                if (isReverse) {
                    mView.setScaleX(1);
                }
            }
        } catch (Exception e) {
            LogUtils.w(e.toString());
        }
    }

}
