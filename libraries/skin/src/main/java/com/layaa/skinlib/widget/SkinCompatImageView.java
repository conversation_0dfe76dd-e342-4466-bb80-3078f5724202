package com.layaa.skinlib.widget;

import android.annotation.SuppressLint;
import android.content.Context;
import android.graphics.Canvas;
import android.util.AttributeSet;
import android.view.MotionEvent;

import com.layaa.skinlib.SkinCompatSupportable;
import com.layaa.skinlib.helper.PressEffectHelper;
import com.layaa.skinlib.helper.SkinCompatImageHelper;

import androidx.appcompat.widget.AppCompatImageView;

/**
 * <AUTHOR>
 * @date 2020-01-07
 * @des
 **/
public class SkinCompatImageView extends AppCompatImageView implements SkinCompatSupportable {
    private SkinCompatImageHelper imageHelper;
    private PressEffectHelper effectHelper;

    public SkinCompatImageView(Context context) {
        this(context, null);
    }

    public SkinCompatImageView(Context context, AttributeSet attrs) {
        this(context, attrs, 0);

    }

    public SkinCompatImageView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        imageHelper = new SkinCompatImageHelper(this);
        imageHelper.loadFromAttributes(attrs, 0);

        effectHelper = new PressEffectHelper(this, attrs, defStyleAttr);
    }

    @Override
    public void applySkin() {
        imageHelper.applySkin();
    }

    @SuppressLint("ClickableViewAccessibility")
    @Override
    public boolean onTouchEvent(MotionEvent event) {
        effectHelper.handlePress(event);
        return super.onTouchEvent(event);
    }

    @Override
    protected void onDraw(Canvas canvas) {
        super.onDraw(canvas);
        effectHelper.drawEffect(canvas);
    }
}
