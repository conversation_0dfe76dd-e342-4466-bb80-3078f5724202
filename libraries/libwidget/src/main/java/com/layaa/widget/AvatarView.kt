package com.layaa.widget

import android.content.Context
import android.text.TextUtils
import android.util.AttributeSet
import android.view.Gravity
import android.widget.FrameLayout
import android.widget.ImageView
import com.layaa.libutils.LayoutUtils
import com.layaa.libutils.UIUtils

/**
 *<AUTHOR>
 *@date 2020/12/11
 *@des 头像框view
 **/
class AvatarView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : FrameLayout(context, attrs, defStyleAttr) {

    private val avatar: ImageView = ImageView(context)
    private var defAvatarSize = UIUtils.getPixels(50F)
    private val headgearView: ImageView = ImageView(context)

    init {
        avatar.scaleType = ImageView.ScaleType.CENTER_CROP
        val avatarParams = LayoutUtils.createFrame(defAvatarSize, defAvatarSize, Gravity.CENTER)
        attachViewToParent(avatar, 0, avatarParams)

        headgearView.scaleType = ImageView.ScaleType.CENTER_CROP
        attachViewToParent(
            headgearView,
            1,
            LayoutUtils.createFrame(LayoutUtils.MATCH_PARENT, LayoutUtils.MATCH_PARENT)
        )

    }

    override fun onMeasure(widthMeasureSpec: Int, heightMeasureSpec: Int) {
        val mode = MeasureSpec.getMode(widthMeasureSpec)
        var newWidthMeasureSpec = widthMeasureSpec
        var newHeightMeasureSpec = widthMeasureSpec
        if (mode != MeasureSpec.EXACTLY) {
            newWidthMeasureSpec = defAvatarSize * AVATAR_PROPORTION.toInt()
            newHeightMeasureSpec = defAvatarSize * AVATAR_PROPORTION.toInt()
        } else {
            val params = avatar.layoutParams
            params?.width =
                (0.5F + MeasureSpec.getSize(newWidthMeasureSpec) * 1F / AVATAR_PROPORTION).toInt()
            params?.height =
                (0.5F + MeasureSpec.getSize(newHeightMeasureSpec) * 1F / AVATAR_PROPORTION).toInt()
        }
        super.onMeasure(newWidthMeasureSpec, newHeightMeasureSpec)
    }

    fun loadAvatar(avatarPath: String?) {
        loadAvatar(avatarPath, avatar.width)
    }

    fun loadAvatar(avatarPath: String?, size: Int) {
        loadImage?.invoke(
            avatar,
            avatarPath,
            (0.5F + size * 1F / AVATAR_PROPORTION).toInt(),
            Type.AVATAR
        )
    }

    fun getAvatarView(): ImageView {
        return avatar
    }

    fun getHeadgearView(): ImageView? {
        return headgearView
    }

    @JvmOverloads
    fun loadHeadgear(resId: Int, size: Int = headgearView.width) {
        loadImage?.invoke(headgearView, resId, size, Type.HEADGEAR)
    }

    @JvmOverloads
    fun loadHeadgear(headgear: String?, size: Int = headgearView.width) {
        if (TextUtils.isEmpty(headgear)) {
            loadHeadgear(R.drawable.icon_head_wear_empty)
            return
        }
        loadImage?.invoke(headgearView, headgear, size, Type.HEADGEAR)
    }

    fun setHeadgearBackground(resId: Int) {
        headgearView.setBackgroundResource(resId)
    }

    fun setAvatarScaleType(scaleType: ImageView.ScaleType) {
        avatar.scaleType = scaleType
    }

    fun setAvatarResId(resId: Int) {
        avatar.setImageResource(resId)
    }

    fun setAvatarBackground(resId: Int) {
        avatar.setBackgroundResource(resId)
    }

    enum class Type(val value: Int) {
        AVATAR(0), HEADGEAR(1)
    }

    companion object {

        /**
         * 头饰占总大小比例
         */
        const val AVATAR_PROPORTION = 1.2F

        @JvmStatic
        var loadImage: ((view: ImageView?, path: Any?, size: Int, type: Type) -> Unit)? = null
    }
}